package com.stt.android.controllers;

import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import androidx.annotation.WorkerThread;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.preference.PreferenceManager;
import androidx.work.WorkManager;
import com.amplitude.api.Amplitude;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.stt.android.analytics.AmplitudeAnalyticsTracker;
import com.stt.android.analytics.AnalyticsUUIDUpdater;
import com.stt.android.analytics.EmarsysAnalytics;
import com.stt.android.analytics.tencent.TencentAnalytics;
import static com.stt.android.data.ConstantsKt.ACTIVITY_DATA_PREFS_NAME;
import com.stt.android.data.activitydata.logout.ActivityDataHelper;
import com.stt.android.data.logout.ClearRoomDbHelper;
import com.stt.android.data.routes.BaseRouteRemoteSyncJob;
import com.stt.android.data.routes.TopRouteCache;
import com.stt.android.data.routes.popular.PopularRouteRemoteSyncJob;
import com.stt.android.data.source.local.smlzip.SMLZipReferenceDao;
import com.stt.android.data.source.local.workouts.AutoLocationSettingStore;
import com.stt.android.data.usersettings.FcmTokenSynchronizer;
import com.stt.android.data.usersettings.UserSettingsSynchronizer;
import com.stt.android.domain.UserSession;
import com.stt.android.domain.database.DatabaseHelper;
import com.stt.android.domain.sml.DeleteSmlDataUseCase;
import com.stt.android.domain.sync.SyncRequest;
import com.stt.android.domain.sync.SyncRequestHandler;
import com.stt.android.domain.user.RankedWorkoutHeader;
import com.stt.android.domain.user.User;
import com.stt.android.domain.user.UserSettings;
import com.stt.android.domain.workout.Workout;
import com.stt.android.domain.workouts.WorkoutDataSource;
import com.stt.android.domain.workouts.WorkoutHeader;
import com.stt.android.exceptions.BackendException;
import com.stt.android.exceptions.InternalDataException;
import com.stt.android.home.people.PeopleController;
import com.stt.android.maps.MapSnapshotter;
import com.stt.android.network.interfaces.ANetworkProvider;
import com.stt.android.session.StartupSync;
import com.stt.android.ui.utils.ToolTipHelper;
import com.stt.android.utils.FileUtils;
import com.stt.android.utils.STTConstants;
import com.stt.android.workouts.DeleteSyncedWorkoutsUseCaseKt;
import com.stt.android.workouts.binary.FsBinaryFileRepository;
import dagger.Lazy;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.locks.ReadWriteLock;
import timber.log.Timber;

/**
 * <p>
 * <em><b>NOTE</b>: Injecting this class can be expensive because it depends on controllers which
 * depend on database
 * initialization</em>
 * </p>
 * <em>As a convention methods starting by get* / store* access DB information, the ones starting
 * by fetch* / push* access information from the backend. Store* access file system</em>
 */
abstract public class BaseSessionController {
    public static final String SIGNING_KEY_TOTP_P3 = "ODcDWz4hekc1QGNTPlciNhEKGl5GPDkzFyVX";
    public static final String LOGIN_SYNC_FINISHED = "LoginSyncFinished";

    final ReadWriteLock sessionLock;
    @SuppressWarnings("WeakerAccess")
    final DatabaseHelper databaseHelper;
    @SuppressWarnings("WeakerAccess")
    final BackendController backendController;
    @SuppressWarnings("WeakerAccess")
    final CurrentUserController currentUserController;
    @SuppressWarnings("WeakerAccess")
    final UserSettingsController userSettingsController;
    @SuppressWarnings("WeakerAccess")
    final WorkoutHeaderController workoutHeaderController;
    @SuppressWarnings("WeakerAccess")
    final WorkoutBinaryController workoutDataController;
    @SuppressWarnings("WeakerAccess")
    final PicturesController picturesController;
    @SuppressWarnings("WeakerAccess")
    final WorkoutCommentController workoutCommentController;
    @SuppressWarnings("WeakerAccess")
    final ReactionModel reactionModel;
    @SuppressWarnings("WeakerAccess")
    final SharedPreferences sharedPreferences;
    @SuppressWarnings("WeakerAccess")
    final Application applicationContext;
    @SuppressWarnings("WeakerAccess")
    final SharedPreferences analyticsPreferences;
    final SharedPreferences activityDataPreferences;
    final SharedPreferences dashboardPreferences;
    final SharedPreferences workoutDefaultSharePreferences;
    final SharedPreferences explorePreferences;
    final AutoLocationSettingStore autoLocationSettingStore;
    private final LoginController loginController;
    @SuppressWarnings("WeakerAccess")
    final PeopleController peopleController;
    @SuppressWarnings("WeakerAccess")
    final VideoModel videoModel;
    @SuppressWarnings("WeakerAccess")
    final LogbookEntryModel logbookEntryModel;
    @SuppressWarnings("WeakerAccess")
    final WorkoutExtensionDataModels workoutExtensionDataModels;
    @SuppressWarnings("WeakerAccess")
    final SMLZipReferenceDao smlZipReferenceDao;

    private final ActivityDataHelper activityDataHelper;

    private final FileUtils fileUtils;
    private final LocalBroadcastManager localBroadcastManager;
    private final EmarsysAnalytics emarsysAnalytics;
    private final TencentAnalytics tencentAnalytics;
    private final AmplitudeAnalyticsTracker amplitudeAnalyticsTracker;
    private final AnalyticsUUIDUpdater analyticsUUIDUpdater;
    /**
     * flag to prevent several simultaneous logins.
     */
    private volatile boolean isLoggingIn = false;
    /**
     * flag to prevent several simultaneous logouts.
     */
    private volatile boolean isLoggingOut = false;

    private final UserSettingsSynchronizer userSettingsSynchronizer;
    private final FcmTokenSynchronizer fcmTokenSynchronizer;
    private final DeleteSmlDataUseCase deleteSmlDataUseCase;
    private final FsBinaryFileRepository binaryFileRepository;
    private final StartupSync startupSync;
    private final WorkoutDataSource workoutDataSource;
    private final SyncRequestHandler syncRequestHandler;
    private final TopRouteCache topRouteCache;
    private final ClearRoomDbHelper clearRoomDbHelper;
    private final Lazy<MapSnapshotter> mapSnapshotter;
    private final dagger.Lazy<WorkManager> workManager;
    public BaseSessionController(ReadWriteLock sessionLock, DatabaseHelper databaseHelper,
        BackendController backendController,
        LoginController loginController,
        CurrentUserController currentUserController,
        UserSettingsController userSettingsController,
        WorkoutHeaderController workoutHeaderController,
        FileUtils fileUtils,
        LocalBroadcastManager localBroadcastManager,
        WorkoutBinaryController workoutDataController,
        PicturesController picturesController,
        Application application,
        WorkoutCommentController workoutCommentController,
        ReactionModel reactionModel,
        PeopleController peopleController,
        EmarsysAnalytics emarsysAnalytics,
        TencentAnalytics tencentAnalytics,
        AmplitudeAnalyticsTracker amplitudeAnalyticsTracker,
        VideoModel videoModel,
        LogbookEntryModel logbookEntryModel,
        WorkoutExtensionDataModels workoutExtensionDataModels,
        SMLZipReferenceDao smlZipReferenceDao,
        ActivityDataHelper activityDataHelper,
        UserSettingsSynchronizer userSettingsSynchronizer,
        FcmTokenSynchronizer fcmTokenSynchronizer,
        DeleteSmlDataUseCase deleteSmlDataUseCase,
        FsBinaryFileRepository binaryFileRepository,
        StartupSync startupSync,
        WorkoutDataSource workoutDataSource,
        SyncRequestHandler syncRequestHandler,
        AnalyticsUUIDUpdater analyticsUUIDUpdater,
        TopRouteCache topRouteCache,
        ClearRoomDbHelper clearRoomDbHelper,
        Lazy<MapSnapshotter> mapSnapshotter,
        dagger.Lazy<WorkManager> workManager
    ) {
        this.sessionLock = sessionLock;
        this.databaseHelper = databaseHelper;
        this.backendController = backendController;
        this.loginController = loginController;
        this.currentUserController = currentUserController;
        this.userSettingsController = userSettingsController;
        this.workoutHeaderController = workoutHeaderController;
        this.workoutDataController = workoutDataController;
        this.fileUtils = fileUtils;
        this.localBroadcastManager = localBroadcastManager;
        this.picturesController = picturesController;
        this.sharedPreferences = PreferenceManager.getDefaultSharedPreferences(application);
        this.applicationContext = application;
        this.autoLocationSettingStore = new AutoLocationSettingStore(application);
        this.analyticsPreferences =
            application.getSharedPreferences(STTConstants.AnalyticsPreferences.ANALYTICS_PREFS_NAME,
                Context.MODE_PRIVATE);
        this.activityDataPreferences =
            application.getSharedPreferences(ACTIVITY_DATA_PREFS_NAME, Context.MODE_PRIVATE);
        this.dashboardPreferences =
            application.getSharedPreferences(STTConstants.DashboardPreferences.PREFS_NAME, Context.MODE_PRIVATE);
        this.workoutDefaultSharePreferences =
            application.getSharedPreferences(STTConstants.DefaultWorkoutSharingPreferences.PREFS_NAME, Context.MODE_PRIVATE);
        this.explorePreferences = application.getSharedPreferences(STTConstants.MapPreferences.EXPLORE_MAP_PREFS_NAME, Context.MODE_PRIVATE);
        this.workoutCommentController = workoutCommentController;
        this.reactionModel = reactionModel;
        this.peopleController = peopleController;
        this.emarsysAnalytics = emarsysAnalytics;
        this.tencentAnalytics = tencentAnalytics;
        this.amplitudeAnalyticsTracker = amplitudeAnalyticsTracker;
        this.videoModel = videoModel;
        this.logbookEntryModel = logbookEntryModel;
        this.workoutExtensionDataModels = workoutExtensionDataModels;
        this.smlZipReferenceDao = smlZipReferenceDao;
        this.activityDataHelper = activityDataHelper;
        this.userSettingsSynchronizer = userSettingsSynchronizer;
        this.fcmTokenSynchronizer = fcmTokenSynchronizer;
        this.deleteSmlDataUseCase = deleteSmlDataUseCase;
        this.binaryFileRepository = binaryFileRepository;
        this.startupSync = startupSync;
        this.workoutDataSource = workoutDataSource;
        this.syncRequestHandler = syncRequestHandler;
        this.analyticsUUIDUpdater = analyticsUUIDUpdater;
        this.topRouteCache = topRouteCache;
        this.clearRoomDbHelper = clearRoomDbHelper;
        this.mapSnapshotter = mapSnapshotter;
        this.workManager = workManager;
    }

    private static List<RankedWorkoutHeader> buildRankedWorkoutHeaders(
        WorkoutHeader referenceWorkout, List<WorkoutHeader> similarWorkoutsByDuration) {
        ArrayList<RankedWorkoutHeader> rankedSimilarWorkoutsByDuration =
            new ArrayList<>(similarWorkoutsByDuration.size());

        if (similarWorkoutsByDuration.isEmpty()) {
            // If there are no similar workouts then we only need to add the reference one as result
            rankedSimilarWorkoutsByDuration.add(
                new RankedWorkoutHeader(referenceWorkout, 1, 100, true));
        } else {
            // Otherwise, iterate over the similar workouts and build the required extra
            // information for ranked headers

            // The maximum total time is the last element of the given list
            double maxTotalTime =
                similarWorkoutsByDuration.get(similarWorkoutsByDuration.size() - 1).getTotalTime();

            boolean referenceWorkoutFound = false;
            // Keep track of the current rank, we can't use the iterator position directly.
            int currentRank = 1;
            // Iterate through the list to find out the rank of each workout and which one should
            // be selected
            for (WorkoutHeader header : similarWorkoutsByDuration) {
                boolean selected = false;
                if (header.getId() == referenceWorkout.getId()) {
                    referenceWorkoutFound = true;
                    selected = true;
                }
                if (!referenceWorkoutFound
                    && header.getTotalTime() > referenceWorkout.getTotalTime()) {
                    Timber.d("SessionController.buildRankedWorkoutHeaders() reference workout not found in results. Inserting it manually");
                    // If we haven't found the reference workout inside the list and the current
                    // header is longer (in time) than the reference one then it means that we
                    // must manually add it to the results before the current one
                    double percentageOfMaxDuration =
                        (referenceWorkout.getTotalTime() / maxTotalTime) * 100;
                    rankedSimilarWorkoutsByDuration.add(
                        new RankedWorkoutHeader(referenceWorkout, currentRank++,
                            percentageOfMaxDuration, true));
                    referenceWorkoutFound = true;
                }
                double percentageOfMaxDuration = (header.getTotalTime() / maxTotalTime) * 100;
                rankedSimilarWorkoutsByDuration.add(
                    new RankedWorkoutHeader(header, currentRank++, percentageOfMaxDuration,
                        selected));
            }

            if (!referenceWorkoutFound) {
                // The reference workout was not found and it's the slowest one (because we
                // didn't find a slower one while iterating). So add it at the end of the list
                double percentageOfMaxDuration =
                    (referenceWorkout.getTotalTime() / maxTotalTime) * 100;
                rankedSimilarWorkoutsByDuration.add(
                    new RankedWorkoutHeader(referenceWorkout, currentRank, percentageOfMaxDuration,
                        true));
            }
        }
        Timber.d("SessionController.buildRankedWorkoutHeaders() built %d results",
            rankedSimilarWorkoutsByDuration.size());
        return rankedSimilarWorkoutsByDuration;
    }

    /**
     * Sync the session for the given {@link UserSession}.
     *
     * @throws IllegalStateException If login was not called before.
     * fixme move all this in SessionInitializer
     */
    @WorkerThread
    public void loginSessionSync(UserSession session) {
        User oldUser = currentUserController.getCurrentUser();
        UserSettings oldUserSettings = userSettingsController.getSettings();
        try {
            clearPreferences();
            startupSync.syncBlocking(session);
            SessionControllerExtKt.syncUserSettingsAndFcmToken(
                fcmTokenSynchronizer, userSettingsSynchronizer, false);

            // updating analytics uuid after login
            analyticsUUIDUpdater.updateUUID(userSettingsController.getSettings().getAnalyticsUUID());

            syncRequestHandler.runRequestInQueueBlocking(SyncRequest.push());
        } catch (Exception e) {
            Timber.w(e, "Couldn't do initial fetch and store, logging user out");
            cleanPartialLogin(session, oldUser, oldUserSettings);
            throw e;
        }
    }

    /**
     * Use to roll-back to the {@code oldUser} and {@code oldUserSettings}
     *
     * @param session the session used to login so we can tell the backend to do a logout on that
     * partial logged in user.
     */
    private void cleanPartialLogin(UserSession session, User oldUser,
        UserSettings oldUserSettings) {
        sessionLock.writeLock().lock();
        try {
            try {
                loginController.logout(session);
            } catch (BackendException e) {
                // Nothing serious if we can't inform the backend about the logout
                Timber.w("Logout failed while cleaning partial login");
            }
            try {
                if (oldUser != null && oldUser.getSession() != null) {
                    Timber.d("cleaning partial login:%s", oldUser.getSession().getSessionKey());
                }
                currentUserController.store(oldUser);
                userSettingsController.storeSettings(oldUserSettings);
                // TODO: remove invites, friends and synchronized workouts
            } catch (InternalDataException e) {
                // Nothing serious if we can't revert the user settings from a partial login
                Timber.w(e,
                    "Something is wrong with local database, unable to revert user settings");
            }
        } finally {
            sessionLock.writeLock().unlock();
        }
    }

    private void clearPreferences() {
        sharedPreferences.edit()
            .remove(STTConstants.DefaultPreferences.KEY_CURRENT_USER_WORKOUTS_LAST_MODIFIED_TIMESTAMP)
            .remove(STTConstants.DefaultPreferences.KEY_NOTIFICATION_LAST_CHECKED_TIMESTAMP)
            .remove(STTConstants.DefaultPreferences.KEY_DASHBOARD_SECONDARY_PAGE)
            .remove(STTConstants.DefaultPreferences.KEY_DASHBOARD_FEED_CONTENT_HASH)
            .remove(STTConstants.DefaultPreferences.KEY_DASHBOARD_SWIPE_FOR_FEEDS_SHOWN)
            .remove(STTConstants.DefaultPreferences.KEY_DASHBOARD_INITIAL_SYNC_DONE)
            .remove(STTConstants.DefaultPreferences.KEY_OPT_IN_DISPLAYED)
            .remove(STTConstants.DefaultPreferences.KEY_RECENT_ACTIVITY_IDS)
            .remove(STTConstants.DefaultPreferences.KEY_PASSWORD_RESET_OR_DELETE_REQUESTED_AT)
            .remove(STTConstants.DefaultPreferences.KEY_LAST_FEED_SYNC_EPOCH_MS)
            .remove(STTConstants.DefaultPreferences.KEY_LAST_STARTUP_SYNC_EPOCH_MS)
            .remove(STTConstants.DefaultPreferences.KEY_JUST_SIGNED_UP_USER)
            .remove(STTConstants.DefaultPreferences.KEY_HAS_RUN_INITIAL_REFRESH)
            .remove(STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_SHOW_PREDICTIONS)
            .remove(STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_STOP_TRACKING_TIMESTAMP)
            .remove(STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_NOTIFY_UPCOMING_PERIOD)
            .remove(STTConstants.DefaultPreferences.KEY_MENSTRUAL_CYCLE_REMIND_LOG_PERIOD)
            .apply();
        autoLocationSettingStore.setAutoLocationSetting(false);
        analyticsPreferences.edit().clear().apply();
        activityDataPreferences.edit().clear().apply();
        dashboardPreferences.edit().clear().apply();
        workoutDefaultSharePreferences.edit().clear().apply();
        explorePreferences.edit().clear().apply();
    }

    /**
     * Notifies interested parties that current user synchronisation has finished
     */
    public void broadcastSyncFinished(boolean loginSyncFinished) {
        Timber.d("Sending broadcast: SYNC_FINISHED");
        localBroadcastManager.sendBroadcast(
            new Intent(STTConstants.BroadcastActions.SYNC_FINISHED).putExtra(
                LOGIN_SYNC_FINISHED, loginSyncFinished));
    }

    @WorkerThread
    public void logout() throws InternalDataException {
        Timber.d("SessionController.logout. Logging in? %s. Logging out? %s", isLoggingIn,
            isLoggingOut);
        if (isLoggingOut) {
            throw new IllegalStateException("Already logging out!");
        }

        isLoggingOut = true;

        try {
            UserSession session;
            sessionLock.readLock().lock();
            boolean isSessionKeyMissing = false;
            try {
                session = currentUserController.getSession();
                if (session == null) {
                    throw new IllegalStateException("Already logged out!");
                }

                if (session.getSessionKey() == null) {
                    Timber.w("Session key missing, logging out without notifying backend");
                    isSessionKeyMissing = true;
                }
            } finally {
                sessionLock.readLock().unlock();
            }

            if (ANetworkProvider.isOnline() && !isSessionKeyMissing) {
                SessionControllerExtKt.syncUserSettingsAndFcmToken(
                    fcmTokenSynchronizer, userSettingsSynchronizer, true);
            }

            try {
                syncRequestHandler.cancelAllBlocking();
                syncRequestHandler.runRequestInQueueBlocking(SyncRequest.push());
            } catch (Exception e) {
                Timber.w(e, "Logout. Could not upload local workout data");
            }

            sessionLock.writeLock().lock();
            try {
                Amplitude.getInstance().uploadEvents();

                BaseRouteRemoteSyncJob.cancelAll(workManager.get());
                PopularRouteRemoteSyncJob.cancelAll(workManager.get());

                releaseResources();
                if (ANetworkProvider.isOnline() && !isSessionKeyMissing) {
                    // If possible let the backend know
                    try {
                        loginController.logout(session);
                    } catch (BackendException e) {
                        // Not big deal, log it and move on.
                        Timber.w("Could not log out from backend");
                    }
                }
                // logout analytics
                amplitudeAnalyticsTracker.logout();
                emarsysAnalytics.logout();
                tencentAnalytics.logout(applicationContext);
                // Reset user and settings to default ones
                Timber.d("Resetting user and settings to ANONYMOUS ones");
                currentUserController.store(User.ANONYMOUS);
                userSettingsController.storeSettings(UserSettings.defaults(applicationContext));
            } finally {
                sessionLock.writeLock().unlock();
            }
            // Used to notify other components that there's been changes in the User information
            broadcastSyncFinished(false);
            String analyticsUUID = userSettingsController.getSettings().getAnalyticsUUID();
            FirebaseCrashlytics.getInstance().setUserId(analyticsUUID);
        } finally {
            isLoggingOut = false;
            Timber.d("SessionController.logout finished. Logging in? %s. Logging out? %s",
                isLoggingIn, isLoggingOut);
        }
    }

    public boolean isLoggingOut() {
        return isLoggingOut;
    }

    private void releaseResources() throws InternalDataException {
        deleteSyncedResources();
        fileUtils.clearCache();
        mapSnapshotter.get().clearCache();
        ExtensionDataModel.Companion.clearCache();
        clearPreferences();
        ToolTipHelper.resetAll(applicationContext);
        workoutDataSource.clearOwnWorkoutsFetchedTimestamp();
        workoutDataSource.clearFolloweesWorkoutsFetchedTimestamp();
        topRouteCache.clearCache();
        clearRoomDbHelper.clearTables();
    }

    private void deleteSyncedResources() throws InternalDataException {
        DeleteSyncedWorkoutsUseCaseKt.deleteSyncedWorkouts(
            workoutHeaderController,
            workoutExtensionDataModels,
            deleteSmlDataUseCase
        );
        picturesController.removeSynced();
        workoutCommentController.empty();
        reactionModel.removeSynced();
        videoModel.removeSynced();
        peopleController.empty();
        deleteOtherSyncedTables();
        delete247Data();
    }

    private void delete247Data() {
        activityDataHelper.delete247Data();
    }

    abstract void deleteOtherSyncedTables() throws InternalDataException;

    /**
     * Retrieves workouts with similar route, activity and username than the provided one.
     *
     * @param referenceWorkout the workout to compare against
     * @return list of similar workouts or empty list if none are found
     */
    public List<RankedWorkoutHeader> getWorkoutsWithSimilarRoute(WorkoutHeader referenceWorkout) {
        sessionLock.readLock().lock();
        try {
            List<WorkoutHeader> similarRouteWorkoutsByDuration =
                workoutHeaderController.findWithSimilarRoute(referenceWorkout);
            return buildRankedWorkoutHeaders(referenceWorkout, similarRouteWorkoutsByDuration);
        } catch (InternalDataException e) {
            Timber.w(e, "Unable to retrieve workouts with similar route");
            return Collections.emptyList();
        } finally {
            sessionLock.readLock().unlock();
        }
    }

    /**
     * Retrieves workouts with similar distance than the provided one
     *
     * @param referenceWorkout the workout to compare against
     * @return list of similar workouts or empty list if none are found
     */
    public List<RankedWorkoutHeader> getWorkoutsWithSimilarDistance(
        WorkoutHeader referenceWorkout) {
        sessionLock.readLock().lock();
        try {
            List<WorkoutHeader> similarDistanceWorkoutsByDuration =
                workoutHeaderController.findWithSimilarDistance(referenceWorkout);
            return buildRankedWorkoutHeaders(referenceWorkout, similarDistanceWorkoutsByDuration);
        } catch (InternalDataException e) {
            Timber.w(e, "Unable to fetch workouts with similar distance");
            return Collections.emptyList();
        } finally {
            sessionLock.readLock().unlock();
        }
    }

    /**
     * @param workoutKey the key to fetch the binary workout from
     * @throws BackendException
     */
    public void fetchWorkoutDataAndSaveToDisk(String workoutKey, String fileName)
        throws BackendException {
        backendController.fetchWorkoutDataAndSave(currentUserController.getSession(),
            workoutKey, fileUtils.getCachedFilePath(STTConstants.DIRECTORY_WORKOUTS, fileName));
        copyWorkoutDataToInternal(fileName);
    }

    /**
     * The geo points have lost accuracy from the binary workout, so we need to overwrite the file.
     * @param fileName
     */
    private void copyWorkoutDataToInternal(String fileName) {
        try {
            FileUtils.copyFile(
                fileUtils.getCachedFilePath(STTConstants.DIRECTORY_WORKOUTS, fileName),
                fileUtils.getInternalFilePath(STTConstants.DIRECTORY_WORKOUTS, fileName), true);
        } catch (Exception e) {
            Timber.d("Unable to copy workout data to internal storage: %s", e.getMessage());
        }
    }

    public void storeWorkout(Workout workout) {
        sessionLock.readLock().lock();
        try {
            binaryFileRepository.storeWorkout(workout);
        } finally {
            sessionLock.readLock().unlock();
        }
    }

    public void storeWorkoutHeader(WorkoutHeader workoutHeader) throws InternalDataException {
        sessionLock.readLock().lock();
        try {
            workoutHeaderController.store(workoutHeader);
        } catch (InternalDataException e) {
            Timber.w(e, "Unable to store workout to the local database");
            throw e;
        } finally {
            sessionLock.readLock().unlock();
        }
    }
}

package com.stt.android.home.dashboardv2.widgets.dataloader

import android.content.Context
import com.stt.android.R
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.currentUserWorkoutUpdated
import com.stt.android.controllers.userSettings
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.user.UserSettings
import com.stt.android.home.dashboard.widget.goal.GoalWidgetData
import com.stt.android.home.dashboard.widget.goal.WeeklyGoalWidgetDataFetcher
import com.stt.android.home.dashboardv2.ui.widgets.common.NO_DATA_VALUE
import com.stt.android.home.dashboardv2.ui.widgets.common.formatWidgetDurationTitle
import com.stt.android.home.dashboardv2.ui.widgets.common.generateDurationTargetSubtitle
import com.stt.android.home.dashboardv2.widgets.DurationWidgetInfo
import com.stt.android.home.dashboardv2.widgets.Period
import com.stt.android.home.dashboardv2.widgets.WidgetType
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.utils.iterator
import com.stt.android.utils.toEpochMilli
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onStart
import javax.inject.Inject
import kotlin.math.abs
import kotlin.math.roundToLong
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

internal class DurationWidgetDataLoader @Inject constructor(
    private val context: Context,
    private val currentUserController: CurrentUserController,
    private val userSettingsController: UserSettingsController,
    private val workoutHeaderController: WorkoutHeaderController,
    private val weeklyGoalWidgetDataFetcher: WeeklyGoalWidgetDataFetcher,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    private val infoModelFormatter: InfoModelFormatter,
) : WidgetDataLoader<DurationWidgetInfo>() {
    override suspend fun load(param: Param): WidgetData<DurationWidgetInfo> {
        val flow = if (param.type == WidgetType.TOTAL_DURATION_THIS_WEEK) {
            combine(
                workoutHeaderController.currentUserWorkoutUpdated
                    .onStart { emit(Unit) },
                userSettingsController.userSettings()
                    .distinctUntilChangedBy(UserSettings::getFirstDayOfTheWeek),
                weeklyGoalWidgetDataFetcher.weeklyGoalWidgetDataFlow(),
            ) { _, _, goal -> loadWidgetInfo(param, goal) }
                .flowOn(coroutinesDispatchers.io)
        } else {
            workoutHeaderController.currentUserWorkoutUpdated
                .map { loadWidgetInfo(param) }
                .onStart { emit(loadWidgetInfo(param)) }
                .flowOn(coroutinesDispatchers.io)
        }
        return WidgetData(
            widgetType = param.type,
            editMode = param.editMode,
            widgetInfo = flow,
        )
    }

    private fun loadWidgetInfo(param: Param, goal: GoalWidgetData? = null): DurationWidgetInfo {
        val period = when (param.type) {
            WidgetType.TOTAL_DURATION_THIS_WEEK -> Period.ThisWeek(userSettingsController)
            WidgetType.TOTAL_DURATION_LAST_7_DAYS -> Period.Last7Days
            WidgetType.TOTAL_DURATION_THIS_MONTH -> Period.ThisMonth
            WidgetType.TOTAL_DURATION_LAST_30_DAYS -> Period.Last30Days
            else -> throw IllegalStateException("Unsupported param: $param")
        }
        val username = currentUserController.username
        val startMillis = period.beginDate.atStartOfDay().toEpochMilli()
        val endMillis = period.endDate.atStartOfDay().toEpochMilli()
        val workouts = fetchWorkoutsByPeriod(
            workoutHeaderController,
            username,
            startMillis,
            endMillis
        )
        val currentPeriodWorkoutsByDay = workouts.groupBy { it.startTime.toLocalDate() }
        val dailyWorkoutTimes = mutableListOf<Double>()
        (period.beginDate..period.endDate).iterator().forEach { day ->
            val workoutsForDay = currentPeriodWorkoutsByDay[day] ?: emptyList()
            dailyWorkoutTimes.add(workoutsForDay.sumOf { it.totalTime })
        }
        val currentPeriodTotalWorkoutSeconds = workouts.sumOf { it.totalTime }
        val (progresses, subtitle, subtitleIconRes) = when (period) {
            is Period.ThisWeek -> {
                goal?.target?.let { goalSeconds ->
                    val exceededSeconds = currentPeriodTotalWorkoutSeconds - goalSeconds
                    val progress = listOf(
                        (currentPeriodTotalWorkoutSeconds / goalSeconds).toFloat().coerceIn(0f, 1f)
                    )

                    val subtitleText = generateDurationTargetSubtitle(context, exceededSeconds, infoModelFormatter)
                    Triple(progress, subtitleText, null)
                } ?: Triple(
                    listOf(NO_DATA_VALUE),
                    context.getString(R.string.widget_no_data_subtitle),
                    null
                )
            }

            Period.Last7Days -> {
                val subtitlePair = formatPeriodChangeSubtitle(period, currentPeriodTotalWorkoutSeconds)
                Triple(
                    generateDailyBarProgresses(dailyWorkoutTimes),
                    subtitlePair.first,
                    subtitlePair.second,
                )
            }

            Period.ThisMonth, Period.Last30Days -> {
                val subtitlePair = formatPeriodChangeSubtitle(period, currentPeriodTotalWorkoutSeconds)
                Triple(
                    generateGrandTotalProgresses(dailyWorkoutTimes),
                    subtitlePair.first,
                    subtitlePair.second,
                )
            }
        }

        val title =
            currentPeriodTotalWorkoutSeconds.coerceAtLeast(0.0).formatWidgetDurationTitle(context)

        return DurationWidgetInfo(
            period = period,
            progresses = progresses,
            title = title,
            subtitle = subtitle,
            subtitleIconRes = subtitleIconRes,
        )
    }

    private fun roundSecondsToMinutes(seconds: Double): Double {
        return (seconds.roundToLong() + 30L)
            .seconds
            .inWholeMinutes
            .minutes
            .inWholeSeconds
            .toDouble()
    }

    private fun formatPeriodChangeSubtitle(
        period: Period,
        currentPeriodTotalWorkoutSeconds: Double
    ): Pair<String, Int?> {
        val (previousPeriodStartMillis, previousPeriodEndMillis) = period.previousPeriod
        val previousPeriodWorkouts = fetchWorkoutsByPeriod(
            workoutHeaderController,
            currentUserController.username,
            previousPeriodStartMillis,
            previousPeriodEndMillis
        )
        val previousPeriodTotalWorkoutSeconds = previousPeriodWorkouts.sumOf { it.totalTime }

        val currentRoundedSeconds = roundSecondsToMinutes(currentPeriodTotalWorkoutSeconds)
        val previousRoundedSeconds = roundSecondsToMinutes(previousPeriodTotalWorkoutSeconds)

        val changeSinceLastPeriod = if (previousRoundedSeconds > 0) {
            currentRoundedSeconds - previousRoundedSeconds
        } else if (currentRoundedSeconds > 0) {
            currentRoundedSeconds
        } else {
            null
        }

        val formatedDuration = changeSinceLastPeriod?.let {
            infoModelFormatter.formatDuration(abs(it).roundToLong())
        } ?: return context.getString(R.string.widget_no_data_subtitle) to null
        val arrowRes =
            if (changeSinceLastPeriod >= 0) R.drawable.widget_up_arrow else R.drawable.widget_down_arrow
        return formatedDuration to arrowRes
    }

    private fun generateGrandTotalProgresses(dailyWorkoutTimes: List<Double>): List<Float> {
        val grandTotalDurations = dailyWorkoutTimes.scanIndexed(0f) { _, acc, value ->
            (acc + value).toFloat()
        }.let { list ->
            // We need at least 2 points in the line chart, otherwise remove the first zero
            if (list.size > 2) {
                list.drop(1)
            } else {
                list
            }
        }

        val maxDurationOfDay = grandTotalDurations.last().coerceAtLeast(1.0f) // Can't be zero
        return if (grandTotalDurations.all { it == 0f }) {
            grandTotalDurations.map { NO_DATA_VALUE }
        } else {
            grandTotalDurations.map {
                it / maxDurationOfDay
            }
        }
    }
}

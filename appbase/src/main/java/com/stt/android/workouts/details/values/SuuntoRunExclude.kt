package com.stt.android.workouts.details.values

import com.stt.android.domain.firmware.Version
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.extensions.SummaryExtension
import com.suunto.connectivity.suuntoconnectivity.device.SuuntoDeviceType

fun isCadenceProcessingRequiredForSuuntoRun(workoutHeader: WorkoutHeader?, summaryExtension: SummaryExtension?) =
    summaryExtension.isCadenceProcessingRequiredForSuuntoRun() && workoutHeader.isCadenceProcessingRequiredForSuuntoRun()

private fun SummaryExtension?.isCadenceProcessingRequiredForSuuntoRun():Boolean =
    isSuuntoRun() && isOneStepIssueNotFixedVersion()

private fun SummaryExtension?.isSuuntoRun(): Boolean {
    return this?.deviceName
        ?.let { SuuntoDeviceType.fromVariantName(it) == SuuntoDeviceType.SuuntoRun }
        ?: false
}

private fun SummaryExtension?.isOneStepIssueNotFixedVersion(): Boolean {
    return this?.deviceSoftwareVersion
        ?.let {
            Version.fromWatchFirmwareName(it)?.let { version ->
                version < Version.fromWatchFirmwareName(
                    ONE_STEP_ISSUE_FIXED_VERSION
                )
            } ?: false
        }
        ?: false
}

private const val ONE_STEP_ISSUE_FIXED_VERSION = "3.19.14"

private val nonOneStepCadenceActivities = setOf(
    ActivityType.JUMP_ROPE,
    ActivityType.INDOOR_ROWING,
    ActivityType.CROSSTRAINER,
    ActivityType.SWIMMING,
    ActivityType.OPENWATER_SWIMMING
)

private fun WorkoutHeader?.isCadenceProcessingRequiredForSuuntoRun(): Boolean {
    return this?.let {
        activityType !in nonOneStepCadenceActivities
    } ?: false
}

package com.stt.android.ui.components.workout

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.net.toUri
import androidx.hilt.navigation.compose.hiltViewModel
import com.stt.android.R
import com.stt.android.compose.component.ActivityIconFlavor
import com.stt.android.compose.component.SuuntoCard
import com.stt.android.compose.modifiers.clickable
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.workout.ActivityType
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.domain.workouts.tag.SuuntoTag
import com.stt.android.newfeed.OnTagClicked
import com.stt.android.viewmodel.sort
import com.stt.android.workoutdetail.tags.TagsData

data class WorkoutShareInfo(
    val selected: Selected,
) {
    sealed interface Selected {
        data object None : Selected
        data object Map : Selected
        data class Image(val index: Int) : Selected
    }
}

@Composable
fun WorkoutCard(
    workoutHeader: WorkoutHeader,
    modifier: Modifier = Modifier,
    viewModel: WorkoutCardViewModel = hiltViewModel(),
    configuration: WorkoutCardViewModel.Configuration = WorkoutCardViewModel.Configuration(),
    onClick: (() -> Unit)? = null,
    onUserClick: ((username: String) -> Unit)? = null,
    onAddPhotoClick: (() -> Unit)? = null,
    onPlayClick: (() -> Unit)? = null,
    onTagClicked: OnTagClicked? = null,
    onAddCommentClick: (() -> Unit)? = null,
    onReactionClick: (() -> Unit)? = null,
    onShareClick: ((WorkoutShareInfo) -> Unit)? = null,
    onEditClick: (() -> Unit)? = null,
    onCoverTouchEvent: ((Int) -> Unit)? = null,
) {
    val viewData = remember { mutableStateOf(WorkoutCardViewData.EMPTY) }
    LaunchedEffect(workoutHeader) {
        viewData.value = WorkoutCardViewData.EMPTY

        val data = viewModel.createViewData(
            config = configuration,
            workoutHeader = workoutHeader,
        )
        if (data != viewData.value) {
            viewData.value = data
        }
    }
    WorkoutCard(
        viewData = viewData.value,
        onClick = onClick,
        onUserClick = onUserClick,
        onAddPhotoClick = onAddPhotoClick,
        onPlayClick = onPlayClick,
        onTagClicked = onTagClicked,
        onAddCommentClick = onAddCommentClick,
        onReactionClick = onReactionClick,
        onShareClick = onShareClick,
        onEditClick = onEditClick,
        onCoverTouchEvent = onCoverTouchEvent,
        modifier = modifier,
    )
}

@Composable
fun WorkoutCard(
    viewData: WorkoutCardViewData,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
    onUserClick: ((username: String) -> Unit)? = null,
    onAddPhotoClick: (() -> Unit)? = null,
    onPlayClick: (() -> Unit)? = null,
    onTagClicked: OnTagClicked? = null,
    onAddCommentClick: (() -> Unit)? = null,
    onReactionClick: (() -> Unit)? = null,
    onShareClick: ((WorkoutShareInfo) -> Unit)? = null,
    onEditClick: (() -> Unit)? = null,
    onCoverTouchEvent: ((Int) -> Unit)? = null,
) {
    SuuntoCard(
        modifier = modifier,
    ) {
        Column(
            modifier = Modifier
                .clickable(onClick = onClick)
                .padding(bottom = MaterialTheme.spacing.medium),
        ) {
            var selectedCover by remember {
                mutableStateOf<WorkoutShareInfo.Selected>(WorkoutShareInfo.Selected.None)
            }

            if (viewData.coverInfo.isNotEmpty()) {
                WorkoutCoverImage(
                    coverInfo = viewData.coverInfo,
                    locationName = viewData.locationName,
                    weatherDrawableRes = viewData.weatherDrawableRes,
                    temperature = viewData.temperature,
                    windDirection = viewData.windDirection,
                    windSpeed = viewData.windSpeed,
                    onAddPhotoClick = onAddPhotoClick?.takeIf { viewData.showAddPhotoButton },
                    onPlayClick = onPlayClick?.takeIf { viewData.showPlayButton },
                    onSelectedCoverChanged = { current -> selectedCover = current },
                    onTouchEvent = onCoverTouchEvent,
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(4f / 3),
                )
            }

            WorkoutSummary(
                activityTypeId = viewData.activityTypeId,
                activityIconFlavor = viewData.activityIconFlavor,
                title = viewData.title,
                subtitle = viewData.subtitle,
                username = viewData.username,
                userImageUrl = viewData.userImageUrl,
                isPrivate = viewData.isPrivate,
                onUserClick = onUserClick,
                modifier = Modifier.padding(
                    top = MaterialTheme.spacing.medium,
                    start = MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                ),
            )

            WorkoutValues(
                workoutValues = viewData.workoutValues,
                modifier = Modifier.padding(
                    top = MaterialTheme.spacing.medium,
                    start = MaterialTheme.spacing.medium,
                ),
            )

            if (viewData.description.isNotBlank()) {
                WorkoutDescription(
                    description = viewData.description,
                    maxLines = viewData.maxDescriptionLines,
                    modifier = Modifier.padding(
                        top = MaterialTheme.spacing.medium,
                        start = MaterialTheme.spacing.medium,
                        end = MaterialTheme.spacing.medium,
                    ),
                )
            }

            if (viewData.achievementIcons.isNotEmpty() || viewData.tags != null) {
                WorkoutAchievementsAndTags(
                    achievementIcons = viewData.achievementIcons,
                    tags = viewData.tags,
                    isSubscribedToPremium = viewData.isSubscribedToPremium,
                    onTagClicked = onTagClicked,
                    modifier = Modifier.padding(
                        top = MaterialTheme.spacing.medium,
                        start = MaterialTheme.spacing.medium,
                        end = MaterialTheme.spacing.medium,
                    ),
                )
            }

            if (viewData.showReactions) {
                WorkoutReaction(
                    commentCount = viewData.commentCount,
                    onAddCommentClick = onAddCommentClick,
                    reactionCount = viewData.reactionCount,
                    reactedByCurrentUser = viewData.reactedByCurrentUser,
                    onReactionClick = onReactionClick,
                    onShareClick = if (viewData.showShareAndEdit && onShareClick != null) {
                        {
                            onShareClick(WorkoutShareInfo(selectedCover))
                        }
                    } else {
                        null
                    },
                    onEditClick = onEditClick?.takeIf { viewData.showShareAndEdit },
                    modifier = Modifier.padding(
                        top = MaterialTheme.spacing.small,
                        start = MaterialTheme.spacing.small,
                        end = MaterialTheme.spacing.small,
                    ),
                )
            }

            if (viewData.commentsToShow.isNotEmpty()) {
                HorizontalDivider(
                    modifier = Modifier.padding(
                        top = MaterialTheme.spacing.small,
                    ),
                    color = Color.LightGray,
                )

                WorkoutComments(
                    commentCount = viewData.commentCount,
                    commentsToShow = viewData.commentsToShow,
                    onUserClick = onUserClick,
                    modifier = Modifier.padding(
                        top = MaterialTheme.spacing.medium,
                        start = MaterialTheme.spacing.medium,
                        end = MaterialTheme.spacing.medium,
                    ),
                )
            }
        }
    }
}

@Preview
@Composable
private fun WorkoutSummaryPreview() {
    M3AppTheme {
        WorkoutCard(
            viewData = WorkoutCardViewData(
                coverInfo = listOf(WorkoutCardViewData.CoverInfo.Image("".toUri())),
                locationName = "Helsinki",
                weatherDrawableRes = R.drawable.ic_weather_broken_clouds_fill,
                temperature = "+8°C",
                windDirection = 135.0F,
                windSpeed = "4m/s",
                showAddPhotoButton = true,
                showPlayButton = true,
                title = "Running",
                subtitle = "Yesterday, 19:36 • Someone with a super long name that doesn't make any sense",
                username = "xizzhu",
                userImageUrl = "",
                workoutValues = listOf(
                    WorkoutCardViewData.WorkoutValue(
                        label = "Duration",
                        value = "1:50'",
                        unit = "",
                    ),
                    WorkoutCardViewData.WorkoutValue(
                        label = "Distance",
                        value = "10",
                        unit = "km",
                    ),
                    WorkoutCardViewData.WorkoutValue(
                        label = "Ascent",
                        value = "433",
                        unit = "m",
                    ),
                ),
                description = "This is a description that is very very long, because I want to check how it looks.\nI mean, why not.",
                maxDescriptionLines = Int.MAX_VALUE,
                achievementIcons = listOf(R.drawable.achievement_trophy_icon),
                tags = TagsData(
                    deviceTag = null,
                    suuntoTags = listOf(SuuntoTag.IMPACT_AEROBIC).sort(),
                    userTags = emptyList(),
                    isOwnWorkout = true,
                ),
                isSubscribedToPremium = false,
                showReactions = true,
                commentCount = 5,
                reactionCount = 8,
                reactedByCurrentUser = false,
                showShareAndEdit = false,
                commentsToShow = listOf(
                    WorkoutCardViewData.Comment(
                        username = "xizzhu",
                        userRealName = "Just a random user",
                        userImageUrl = "",
                        comment = "Just a random comment that is probably very long that can probably end up in multiple lines.",
                    ),
                ),
                activityTypeId = ActivityType.RUNNING.id,
                activityIconFlavor = ActivityIconFlavor.FILL_WITH_OPACITY,
                isPrivate = true,
            ),
        )
    }
}

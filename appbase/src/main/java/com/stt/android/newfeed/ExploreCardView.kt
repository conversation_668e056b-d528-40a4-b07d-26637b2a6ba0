package com.stt.android.newfeed

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.annotation.AttrRes
import androidx.annotation.StyleRes
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.airbnb.epoxy.AfterPropsSet
import com.airbnb.epoxy.ModelProp
import com.airbnb.epoxy.ModelView
import com.stt.android.R
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.databinding.ViewholderFeedCardExploreBinding
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.social.userprofileV2.BaseUserProfileActivity
import com.stt.android.ui.components.workout.WorkoutCard

@ModelView
class ExploreCardView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    @AttrRes defStyleAttr: Int = 0,
    @StyleRes defStyleRes: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr, defStyleRes) {
    @ModelProp(ModelProp.Option.DoNotHash)
    lateinit var rewriteNavigator: WorkoutDetailsRewriteNavigator

    @set:[ModelProp]
    lateinit var workoutCards: List<WorkoutCardInfo>

    private val binding: ViewholderFeedCardExploreBinding = ViewholderFeedCardExploreBinding.inflate(
        LayoutInflater.from(context), this, true)

    @AfterPropsSet
    fun setup() {
        binding.composeView.setContentWithM3Theme {
            ExploreCard(
                workouts = workoutCards,
                onClick = { workoutHeader ->
                    rewriteNavigator.navigate(
                        context = context,
                        username = workoutHeader.username,
                        workoutId = workoutHeader.id,
                        workoutKey = workoutHeader.key,
                    )
                },
                onUserClick = { username ->
                    context.startActivity(BaseUserProfileActivity.newStartIntent(context, username, false))
                },
                modifier = Modifier.padding(top = MaterialTheme.spacing.medium),
            )
        }
    }
}

@Composable
private fun ExploreCard(
    workouts: List<WorkoutCardInfo>,
    onClick: (WorkoutHeader) -> Unit,
    onUserClick: ((username: String) -> Unit),
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier,
    ) {
        HorizontalDivider()

        Text(
            text = stringResource(R.string.shared_nearby),
            modifier = Modifier.padding(MaterialTheme.spacing.medium),
            style = MaterialTheme.typography.bodyLargeBold,
        )

        LazyRow(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = MaterialTheme.spacing.medium),
        ) {
            items(workouts) { workoutCardInfo ->
                WorkoutCard(
                    viewData = workoutCardInfo.workoutCardViewData,
                    onClick = { onClick(workoutCardInfo.workoutHeader) },
                    modifier = Modifier
                        .padding(start = MaterialTheme.spacing.medium)
                        .width(312.dp),
                    onUserClick = onUserClick,
                )
            }

            item {
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.medium))
            }
        }

        HorizontalDivider()
    }
}

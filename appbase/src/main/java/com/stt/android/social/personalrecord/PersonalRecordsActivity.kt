package com.stt.android.social.personalrecord

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.compose.util.rememberEventThrottler
import com.stt.android.compose.util.setContentWithTheme
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.utils.STTConstants.ExtraKeys.NAVIGATED_FROM_SOURCE
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject

@AndroidEntryPoint
class PersonalRecordsActivity : AppCompatActivity() {

    @Inject
    lateinit var rewriteNavigator: WorkoutDetailsRewriteNavigator

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentWithTheme {
            val eventThrottler = rememberEventThrottler()
            PersonalRecordsScreen(
                onCloseClicked = {
                    finish()
                },
                onItemClick = { recordItemDomain ->
                    eventThrottler.processEvent {
                        recordItemDomain.workoutHeader?.let {
                            rewriteNavigator.navigate(
                                context = this,
                                username = it.username,
                                workoutId = it.id,
                                workoutKey = it.key,
                                analyticsSource = AnalyticsPropertyValue.WorkoutDetailsSourceProperty.PERSONAL_RECORD,
                                isFromNotification = false,
                                showCommentsDialog = false,
                            )
                        }
                    }
                }
            )
        }
    }
    companion object {
        fun newIntent(context: Context, analyticsSource: String): Intent {
            return Intent(context, PersonalRecordsActivity::class.java)
                .apply {
                    putExtra(NAVIGATED_FROM_SOURCE, analyticsSource)
                }
        }

    }
}

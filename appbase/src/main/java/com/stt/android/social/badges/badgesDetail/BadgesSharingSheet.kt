package com.stt.android.social.badges.badgesDetail

import android.graphics.Bitmap
import android.view.View
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.MoreVert
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.ShapeDefaults
import androidx.compose.material3.SheetState
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.boundsInWindow
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.view.drawToBitmap
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.allowHardware
import coil3.request.crossfade
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.DraggableBottomSheetHandle
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.core.R
import com.stt.android.data.badges.UserBadge
import com.stt.android.domain.user.User
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BadgesSharingSheet(
    badgesDetailLoad: BadgesDetailViewData.Loaded,
    user: User,
    onScreenshot: (Bitmap, Boolean) -> Unit,
    onDismissRequest: () -> Unit,
    modifier: Modifier = Modifier,
    sheetState: SheetState = rememberModalBottomSheetState()
) {
    ModalBottomSheet(
        sheetState = sheetState,
        onDismissRequest = onDismissRequest,
        modifier = modifier,
        containerColor = MaterialTheme.colorScheme.surface,
        dragHandle = null,
    ) {
        DraggableBottomSheetHandle(
            topPadding = MaterialTheme.spacing.medium,
            bottomPadding = MaterialTheme.spacing.xlarge
        )
        var screenshotBounds by remember { mutableStateOf(Rect.Zero) }
        val view = LocalView.current
        Column(
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium),
        ) {
            BadgesSharingContent(
                badgesDetailLoad = badgesDetailLoad,
                user = user,
                modifier = Modifier.fillMaxWidth(),
                onScreenshotBounds = {
                    screenshotBounds = it
                }
            )
            Row(
                modifier = Modifier.padding(
                    top = MaterialTheme.spacing.xlarge,
                    bottom = MaterialTheme.spacing.medium
                )
            ) {
                OutlinedButton(
                    onClick = {
                        onScreenshot(screenshotBitmap(screenshotBounds, view), false)
                    },
                    modifier = Modifier
                        .weight(1f)
                        .height(dimensionResource(R.dimen.new_design_button_height)),
                    border = BorderStroke(
                        width = MaterialTheme.spacing.xxxsmall,
                        color = MaterialTheme.colorScheme.primary
                    ),
                    shape = MaterialTheme.shapes.small
                ) {
                    Text(
                        text = stringResource(id = com.stt.android.R.string.save),
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.primary,
                        fontWeight = FontWeight.Bold
                    )
                }
                PrimaryButton(
                    text = stringResource(com.stt.android.R.string.share), onClick = {
                        onScreenshot(screenshotBitmap(screenshotBounds, view), true)
                    }, modifier = Modifier
                        .weight(1f)
                        .padding(start = MaterialTheme.spacing.xsmaller),
                    buttonHeight = dimensionResource(R.dimen.new_design_button_height)
                )
            }
        }
    }
}

private fun screenshotBitmap(
    bounds: Rect,
    view: View,
): Bitmap {
    return view.drawToBitmap().let { bitmap ->
        Bitmap.createBitmap(
            bitmap,
            bounds.left.toInt(),
            bounds.top.toInt(),
            bounds.width.toInt(),
            bounds.height.toInt()
        )
    }
}

@Composable
private fun BadgesSharingContent(
    badgesDetailLoad: BadgesDetailViewData.Loaded,
    user: User,
    onScreenshotBounds: (Rect) -> Unit,
    modifier: Modifier = Modifier
) {
    val badgesDetail = badgesDetailLoad.badgesDetail
    Card(shape = MaterialTheme.shapes.small, modifier = modifier.onGloballyPositioned {
        onScreenshotBounds(it.boundsInWindow())
    }) {
        Box(modifier = Modifier) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(badgesDetail.badgeBackgroundImageUrl).allowHardware(false).build(),
                contentDescription = null,
            )
            Column(
                modifier = Modifier.padding(MaterialTheme.spacing.medium)
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(user.profileImageUrl)
                            .crossfade(true).placeholderWithFallback(
                                LocalContext.current,
                                R.drawable.ic_default_profile_image_light
                            ).transformations(CircleCropTransformation()).allowHardware(false)
                            .build(),
                        contentDescription = null,
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape),
                        contentScale = ContentScale.Crop,
                    )
                    Text(
                        modifier = Modifier.padding(start = MaterialTheme.spacing.small),
                        text = user.realName ?: user.username,
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                Box(
                    modifier = Modifier.padding(top = MaterialTheme.spacing.large),
                    contentAlignment = Alignment.TopCenter
                ) {
                    Card(
                        shape = ShapeDefaults.Medium, modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 74.dp),
                        colors = CardDefaults.cardColors()
                            .copy(containerColor = MaterialTheme.colorScheme.surface)
                    ) {
                        Spacer(modifier = Modifier.height(74.dp))
                        Column(
                            modifier = Modifier
                                .padding(MaterialTheme.spacing.medium)
                                .fillMaxWidth(),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = badgesDetail.badgeName ?: "",
                                style = MaterialTheme.typography.headlineSmall,
                                color = MaterialTheme.colorScheme.onSurface,
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = stringResource(
                                    com.stt.android.R.string.badges_earned_ranking,
                                    badgesDetail.acquisitionRanking?.toString() ?: ""
                                ),
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurface,
                                modifier = Modifier.padding(top = MaterialTheme.spacing.small)
                            )
                            badgesDetail.acquisitionTime?.let {
                                Text(
                                    text = DateTimeFormatter.ofLocalizedDate(FormatStyle.MEDIUM)
                                        .format(
                                            LocalDateTime.ofInstant(
                                                Instant.ofEpochMilli(it),
                                                ZoneId.systemDefault()
                                            )
                                        ),
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSurface,
                                    modifier = Modifier.padding(top = MaterialTheme.spacing.small)
                                )
                            }
                            val badgesAchievementDataList =
                                badgesDetailLoad.badgesAchievementDataList
                            if (!badgesAchievementDataList.isNullOrEmpty()) {
                                Card(
                                    modifier = Modifier.padding(top = MaterialTheme.spacing.large),
                                    shape = MaterialTheme.shapes.large,
                                    border = BorderStroke(
                                        MaterialTheme.spacing.xxxsmall,
                                        MaterialTheme.colorScheme.dividerColor
                                    ),
                                    colors = CardDefaults.cardColors()
                                        .copy(containerColor = MaterialTheme.colorScheme.dividerColor)
                                ) {
                                    // achievement data is limited to 4 items
                                    val limitedAchievementData = badgesAchievementDataList.take(4)
                                    // because I implement the grid divider line by using the grid item spacing.
                                    // I need to fill the LazyVerticalGrid to avoid the last item background is divider color
                                    val fillContentData =
                                        if (limitedAchievementData.size % 2 == 0) {
                                            limitedAchievementData
                                        } else {
                                            limitedAchievementData + BadgesAchievementData(
                                                AnnotatedString(""),
                                                ""
                                            )
                                        }
                                    LazyVerticalGrid(
                                        modifier = Modifier.heightIn(max = 108.dp),
                                        columns = GridCells.Fixed(2),
                                        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxxsmall),
                                        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxxsmall),
                                    ) {
                                        items(fillContentData.size) { index ->
                                            val item = fillContentData[index]
                                            Column(
                                                modifier = Modifier
                                                    .background(MaterialTheme.colorScheme.surface)
                                                    .height(54.dp)
                                                    .padding(horizontal = MaterialTheme.spacing.medium),
                                                verticalArrangement = Arrangement.Center,
                                            ) {
                                                Text(
                                                    text = item.explanation,
                                                    style = MaterialTheme.typography.bodySmall,
                                                    color = MaterialTheme.colorScheme.onSurface,
                                                    maxLines = 1,
                                                    overflow = TextOverflow.Ellipsis
                                                )
                                                Text(
                                                    text = item.value,
                                                    style = MaterialTheme.typography.bodyLarge,
                                                    color = MaterialTheme.colorScheme.onSurface,
                                                    fontWeight = FontWeight.Bold,
                                                    modifier = Modifier.padding(top = MaterialTheme.spacing.xsmall),
                                                    maxLines = 1,
                                                    overflow = TextOverflow.Ellipsis
                                                )
                                            }
                                        }
                                    }
                                    HorizontalDivider()
                                    val activityIds = badgesDetail.activityIds
                                    if (!activityIds.isNullOrEmpty()) {
                                        Row(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .background(MaterialTheme.colorScheme.surface)
                                                .padding(vertical = MaterialTheme.spacing.medium)
                                        ) {
                                            activityIds.take(9).forEach {
                                                SuuntoActivityIcon(
                                                    it,
                                                    modifier = Modifier.padding(horizontal = MaterialTheme.spacing.xsmall),
                                                    iconSize = MaterialTheme.iconSizes.small
                                                )
                                            }
                                            if (activityIds.size > 9) {
                                                Icon(
                                                    imageVector = Icons.Filled.MoreVert,
                                                    contentDescription = null,
                                                    modifier = Modifier.rotate(90f)
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                            Image(
                                modifier = Modifier.padding(
                                    top = MaterialTheme.spacing.large,
                                    bottom = MaterialTheme.spacing.small
                                ),
                                painter = painterResource(com.stt.android.R.drawable.share_summary_logo),
                                contentDescription = null
                            )
                        }
                    }
                    AsyncImage(
                        model = ImageRequest.Builder(LocalContext.current)
                            .data(badgesDetail.acquiredBadgeIconUrl).allowHardware(false).build(),
                        contentDescription = null,
                        modifier = Modifier.size(148.dp),
                        contentScale = ContentScale.Crop
                    )
                }
            }

        }

    }
}

@Preview(showBackground = true)
@Composable
private fun BadgesSharingSheetPreview() {
    val user = User(
        id = 1,
        username = "testuser",
        realName = "测试用户",
        profileImageUrl = "",
        key = "",
        website = null,
        city = null,
        country = null,
        profileImageKey = null,
    )
    val badgesDetailLoad = BadgesDetailViewData.Loaded(
        badgesDetail = UserBadge(
            badgeName = "Free runner",
            badgeBackgroundImageUrl = "",
            acquiredBadgeIconUrl = "",
            acquisitionRanking = 1,
            acquisitionTime = System.currentTimeMillis(),
            activityIds = listOf(1, 2, 3, 4, 5, 6, 7, 8, 9, 10),
            badgeIconUrl = "",
            badgeDesc = "这是一个测试徽章的描述",
            badgeType = null,
            acquisitionCondition = null,
            startTime = System.currentTimeMillis() - 1000000,
            endTime = System.currentTimeMillis() + 1000000,
            acquisitionWayType = null,
            participationTime = System.currentTimeMillis(),
            transcriptDataFields = listOf("", ""),
            maxSpeed = 10f,
            maxDistance = 100.0,
            totalDistance = 500.0,
            activitySessions = 5,
            totalDuration = 3600.0,
            energy = 1000,
            totalWorkoutDays = 30,
            totalAscent = 1000,
            maxPace = 5.0,
            maxCyclingSpeed = 20f,
            maxDivingDepth = 30.0,
            maxDuration = 7200.0,
            totalUTMBDistance = 200.0,
            createTime = System.currentTimeMillis(),
            badgeStatus = null
        ),
        badgesAchievementDataList = listOf(
            BadgesAchievementData(AnnotatedString("6'53 /km"), "Max. pace"),
            BadgesAchievementData(AnnotatedString("58.90 km"), "Max. distance"),
            BadgesAchievementData(AnnotatedString("6'53 /km"), "Max. pace"),
        ),
        conditionData = emptyList(),
        exploreMore = emptyList(),
    )
    M3AppTheme {
        BadgesSharingContent(
            badgesDetailLoad = badgesDetailLoad,
            user = user,
            onScreenshotBounds = {}
        )
    }
}

package com.stt.android.social.badges.myBadgesList

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.coroutines.runSuspendCatching
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
internal class MyBadgesListViewModel @Inject constructor(
    private val dataLoader: MyBadgesListDataLoader
) : ViewModel() {

    private val _uiState = MutableStateFlow<MyBadgesListViewData>(MyBadgesListViewData.Initial)
    val uiState: StateFlow<MyBadgesListViewData> = _uiState.asStateFlow()

    init {
        loadMyBadgesList()
    }

    private fun loadMyBadgesList() {
        viewModelScope.launch {
            runSuspendCatching {
                val result = dataLoader.loadMyBadgesList()
                _uiState.value = MyBadgesListViewData.Loaded(
                    myBadgesList = result
                )
            }.onFailure {
                Timber.w(it,"Fail to load my badges")
                _uiState.value = MyBadgesListViewData.Initial
            }

        }
    }
}

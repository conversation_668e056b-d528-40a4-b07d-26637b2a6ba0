package com.stt.android.social.badges.badgesDetail

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.core.content.FileProvider
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.sharing.AnalysisData
import com.stt.android.sharing.SharingHelper
import com.stt.android.sharing.SharingInfo
import com.stt.android.sharing.SharingType
import com.stt.android.social.badges.badgesbase.BadgesLoadingScreen
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class BadgeDetailActivity : AppCompatActivity() {
    private val viewModel: BadgesDetailViewModel by viewModels()

    @Inject
    lateinit var sharingHelper: SharingHelper

    @OptIn(ExperimentalMaterial3Api::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val badgeConfigId = intent.getStringExtra(EXTRA_BADGE_CONFIG_ID)
        if (badgeConfigId == null) {
            finish()
            return
        }
        viewModel.loadBadgeDetail(badgeConfigId)
        setContent {
            M3AppTheme {
                val viewData by viewModel.uiState.collectAsState()
                when (viewData) {
                    is BadgesDetailViewData.Loaded -> {
                        var showSharingDialog by remember { mutableStateOf(false) }
                        BadgeDetailScreen(
                            viewData = viewData as BadgesDetailViewData.Loaded,
                            onEvent = { event ->
                                when (event) {
                                    BadgeDetailViewEvent.Close -> finish()
                                    is BadgeDetailViewEvent.OnExploreBadgesClick ->
                                        startActivity(
                                            newIntent(this, event.badgesId)
                                        )

                                    is BadgeDetailViewEvent.Share -> {
                                        showSharingDialog = true
                                    }
                                }
                            },
                            modifier = Modifier
                        )
                        if (showSharingDialog) {
                            BadgesSharingSheet(
                                badgesDetailLoad = viewData as BadgesDetailViewData.Loaded,
                                user = viewModel.user.collectAsState().value,
                                onDismissRequest = { showSharingDialog = false },
                                sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true),
                                onScreenshot = { bitmap, sharing ->
                                    if (sharing) {
                                        viewModel.saveBadgesImageToCache(
                                            externalCacheDir ?: cacheDir, bitmap
                                        ) {
                                            val shareImageUri = FileProvider.getUriForFile(
                                                this,
                                                "$packageName.FileProvider",
                                                it
                                            )
                                            val sharingInfo = SharingInfo(
                                                sharingType = SharingType.MEDIA,
                                                resourceUris = listOf(shareImageUri)
                                            )
                                            val shareByApp = sharingHelper.shareByApp(sharingInfo ,supportFragmentManager)
                                            if (!shareByApp) {
                                                sharingHelper.shareBySystem(
                                                    sharingInfo,
                                                    this,
                                                    // TODO implement analytics event
                                                    AnalysisData(eventName = "", emptyMap())

                                                )
                                            }
                                        }
                                    } else {
                                        viewModel.saveBadgesImageToMedia(contentResolver, bitmap)
                                        showSharingDialog = false
                                    }
                                }
                            )
                        }
                    }

                    is BadgesDetailViewData.Initial -> {
                        Scaffold(
                            topBar = {
                                SuuntoTopBar(
                                    title = "",
                                    onNavigationClick = { finish() },
                                )
                            },
                        ) { paddingValues ->
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .narrowContentWithBgColors(
                                        backgroundColor = MaterialTheme.colorScheme.surface,
                                        outerBackgroundColor = MaterialTheme.colorScheme.background
                                    )
                                    .padding(paddingValues)
                                    .verticalScroll(rememberScrollState())
                            ) {
                                BadgesLoadingScreen()
                            }
                        }
                    }

                    is BadgesDetailViewData.Error -> {
                        finish()
                        Timber.w("Fail to load badges detail page.")
                    }
                }
            }
        }
    }

    companion object {
        private const val EXTRA_BADGE_CONFIG_ID = "extra_badge_config_id"
        fun newIntent(context: Context, badgeConfigId: String): Intent {
            return Intent(context, BadgeDetailActivity::class.java).apply {
                putExtra(EXTRA_BADGE_CONFIG_ID, badgeConfigId)
            }
        }
    }
}


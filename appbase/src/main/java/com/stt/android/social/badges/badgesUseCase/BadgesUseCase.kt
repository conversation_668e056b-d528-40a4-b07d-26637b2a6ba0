package com.stt.android.social.badges.badgesUseCase

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.data.badges.BadgeModuleConfig
import com.stt.android.data.badges.BadgesRemoteDataSource
import com.stt.android.data.badges.ExploreMore
import com.stt.android.data.badges.UserBadge
import com.stt.android.data.badges.UserBadgeList
import kotlinx.coroutines.withContext
import javax.inject.Inject

class BadgesBaseUseCase @Inject constructor(
    private val badgesDataSource: BadgesRemoteDataSource,
    private val dispatchers: CoroutinesDispatchers,
) {
    suspend fun getBadgeConfigs(module: String? = null): List<BadgeModuleConfig> =
        withContext(dispatchers.io) {
            badgesDataSource.getBadgeConfigs(module)
        }

    suspend fun getUserBadgeList(): UserBadgeList =
        withContext(dispatchers.io) {
            badgesDataSource.getUserBadgeList()
        }
}

class BadgesDetailUseCase @Inject constructor(
    private val badgesDataSource: BadgesRemoteDataSource,
    private val dispatchers: CoroutinesDispatchers,
){
    suspend fun getMoreBadgesInDetail(badgeConfigId: String?): List<ExploreMore> =
        withContext(dispatchers.io) {
            badgesDataSource.getMoreBadgesInDetail(badgeConfigId)
        }

    suspend fun getUserBadgeDetail(badgeConfigId: String? = null): UserBadge =
        withContext(dispatchers.io) {
            badgesDataSource.getUserBadgeDetail(badgeConfigId)
        }
}

class MyBadgesListUseCase @Inject constructor(
    private val badgesDataSource: BadgesRemoteDataSource,
    private val dispatchers: CoroutinesDispatchers,
){
    suspend fun getUserBadgeList(): UserBadgeList =
        withContext(dispatchers.io) {
            badgesDataSource.getUserBadgeList()
        }
}

class BadgesListUseCase @Inject constructor(
    private val badgesDataSource: BadgesRemoteDataSource,
    private val dispatchers: CoroutinesDispatchers,
){
    suspend fun getBadgeConfigs(module: String? = null): List<BadgeModuleConfig> =
        withContext(dispatchers.io) {
            badgesDataSource.getBadgeConfigs(module)
        }

    suspend fun getUserBadgeList(): UserBadgeList =
        withContext(dispatchers.io) {
            badgesDataSource.getUserBadgeList()
        }
}

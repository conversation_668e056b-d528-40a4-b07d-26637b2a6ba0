package com.stt.android.social.badges.badgesList

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.viewModels
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.social.badges.badgesDetail.BadgeDetailActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class BadgesListActivity : ComponentActivity() {

    private val viewModel: BadgesListViewModel by viewModels()

    private fun handleBadgesListEvent(event: BadgesListViewEvent) {
        when (event) {
            is BadgesListViewEvent.Close -> {
                finish()
            }

            is BadgesListViewEvent.OnListBadgesClick -> startActivity(
                BadgeDetailActivity.newIntent(this, event.badgesId)
            )
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        val moduleName = intent.getStringExtra(EXTRA_MODULE_NAME)
        if (moduleName == null) {
            finish()
            return
        }

        viewModel.loadBadges(moduleName)
        setContentWithM3Theme {
            val viewData by viewModel.uiState.collectAsState()

            BadgesListScreen(
                viewData = viewData,
                onEvent = ::handleBadgesListEvent
            )
        }
    }

    companion object {
        private const val EXTRA_MODULE_NAME = "extra_module_name"

        fun newIntent(context: Context, moduleName: String): Intent {
            return Intent(context, BadgesListActivity::class.java).apply {
                putExtra(EXTRA_MODULE_NAME, moduleName)
            }
        }
    }
}

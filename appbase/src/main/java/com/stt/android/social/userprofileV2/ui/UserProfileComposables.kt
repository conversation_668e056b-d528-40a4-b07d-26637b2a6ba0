package com.stt.android.social.userprofileV2.ui

import androidx.annotation.DrawableRes
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.layout.ContentScale
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.pluralStringResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import coil3.request.crossfade
import com.stt.android.R
import com.stt.android.compose.component.SuuntoActivityIcon
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.cloudyGrey
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.lightGrey
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyMegaBold
import com.stt.android.compose.theme.material3.bodyXLargeBold
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.SubscriptionInfo
import com.stt.android.domain.user.UserSubscription
import com.stt.android.domain.user.subscription.CurrentPremiumSubscriptionStatus
import com.stt.android.domain.workout.ActivityType
import com.stt.android.follow.FollowStatus
import com.stt.android.home.dashboardv2.ui.widgets.common.formatWidgetDurationTitle
import com.stt.android.social.badges.myBadgesList.MyBadgesDisplayItem
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.FriendStatus
import com.stt.android.social.friends.composables.OutlinedFriendStatusButton
import com.stt.android.social.userprofileV2.DeviceType
import com.stt.android.social.userprofileV2.FollowCountStats
import com.stt.android.social.userprofileV2.SettingMenuInfo
import com.stt.android.social.userprofileV2.SettingMenuType
import com.stt.android.social.userprofileV2.TopActivity
import com.stt.android.social.userprofileV2.WorkoutSummaryStats
import com.stt.android.ui.utils.TextFormatter
import java.math.BigDecimal
import java.math.RoundingMode

@Composable
fun ProfileInfo(
    isCurrentUser: Boolean,
    username: String,
    description: String?,
    modifier: Modifier = Modifier,
    followersSummary: FollowCountStats? = null,
    deviceTypeList: List<DeviceType>? = null,
    onFollowingClicked: (count: Int) -> Unit = {},
    onFollowersClicked: (count: Int) -> Unit = {},
    onFollowButtonClicked: () -> Unit = {},
    friend: Friend? = null,
    blocked: Boolean = false,
) {
    val displayDescription = description?.takeIf { it.isNotBlank() }
        ?: isCurrentUser.takeIf { it }?.let {
            stringResource(R.string.profile_description_default)
        }
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmaller),
        modifier = modifier.padding(vertical = MaterialTheme.spacing.medium),
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = MaterialTheme.spacing.xlarge),
        ) {
            Text(
                text = username,
                style = MaterialTheme.typography.bodyMegaBold,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.padding(horizontal = MaterialTheme.spacing.xlarge),
            )
            if (!displayDescription.isNullOrBlank()) {
                Text(
                    text = displayDescription,
                    style = MaterialTheme.typography.body,
                    textAlign = TextAlign.Center,
                )
            }
        }

        deviceTypeList?.takeIf { it.isNotEmpty() }?.let {
            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
                modifier = Modifier.padding(vertical = MaterialTheme.spacing.xsmall),
                maxItemsInEachRow = 3,
            ) {
                deviceTypeList.forEach { deviceType ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
                    ) {
                        if (deviceType.icon != null) {
                            Icon(
                                painter = painterResource(id = deviceType.icon),
                                contentDescription = null,
                                modifier = Modifier.size(MaterialTheme.iconSizes.mini),
                                tint = MaterialTheme.colorScheme.secondary,
                            )
                        }
                        Text(
                            text = deviceType.deviceName,
                            style = MaterialTheme.typography.bodySmall,
                            color = MaterialTheme.colorScheme.secondary,
                        )
                    }
                }
            }
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.Center,
        ) {
            FollowStats(
                count = followersSummary?.followingCount,
                text = stringResource(R.string.following).lowercase(),
                onFollowClick = onFollowingClicked
            )
            VerticalDivider(
                color = MaterialTheme.colorScheme.lightGrey,
                modifier = Modifier
                    .height(MaterialTheme.spacing.smaller)
                    .padding(horizontal = MaterialTheme.spacing.medium)
                    .align(Alignment.CenterVertically),
            )
            FollowStats(
                count = followersSummary?.followersCount,
                text = stringResource(R.string.followers).lowercase(),
                onFollowClick = onFollowersClicked
            )
        }

        friend?.let {
            OutlinedFriendStatusButton(
                it.friendStatus,
                blocked,
                onClick = onFollowButtonClicked,
            )
        }
    }
}

@Composable
private fun FollowStats(
    count: Int?,
    text: String,
    modifier: Modifier = Modifier,
    onFollowClick: (count: Int) -> Unit = {}
) {

    val countString = when {
        count == null -> "--"
        count < 10000 -> count.toString()
        count % 1000 == 0 -> "${count / 1000}k"
        else -> {
            val countInK = count / 1000.0
            val formatted = BigDecimal(countInK)
                .setScale(2, RoundingMode.DOWN)
                .toPlainString()
            "${formatted}k"
        }
    }


    Box(
        modifier = modifier.clickableThrottleFirst { onFollowClick(count ?: 0) },
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(MaterialTheme.spacing.xsmall),
        ) {
            Text(
                modifier = Modifier.padding(top = MaterialTheme.spacing.xxsmall),
                text = countString,
                style = MaterialTheme.typography.bodyXLargeBold,
            )
            Text(
                text = text,
                style = MaterialTheme.typography.body,
            )
        }
    }
}

@Composable
fun ActivityStatsSection(
    unit: MeasurementUnit,
    modifier: Modifier = Modifier,
    workoutSummaryStats: WorkoutSummaryStats? = null,
    onActivityClick: () -> Unit = {}
) {
    val placeholder = AnnotatedString("--")
    val distance = workoutSummaryStats?.totalDistanceSum
        ?.let {
            var formattedDistance = TextFormatter.formatDistanceRounded(unit.toDistanceUnit(it))
            if ((formattedDistance.toDoubleOrNull() ?: 0.0) > 10_000) {
                formattedDistance = "${formattedDistance.toDoubleOrNull()?.div(1000)?.toInt()}K"
            }
            buildAnnotatedString {
                append(formattedDistance)
                append(" ")
                withStyle(SpanStyle(fontSize = MaterialTheme.typography.bodySmall.fontSize)) {
                    append(stringResource(unit.distanceUnit))
                }
            }
        }
        ?: placeholder
    val days = workoutSummaryStats?.totalDays
        ?.let { AnnotatedString(it.toString()) }
        ?: placeholder
    val activityCount = workoutSummaryStats?.totalNumberOfWorkoutsSum
        ?.let { AnnotatedString(it.toString()) }
        ?: placeholder
    val duration = workoutSummaryStats?.totalTimeSum
        ?.formatWidgetDurationTitle(LocalContext.current)
        ?: placeholder
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.spacing.medium)
            .background(MaterialTheme.colorScheme.surface)
            .clip(MaterialTheme.shapes.large)
            .border(
                width = 1.dp,
                color = MaterialTheme.colorScheme.dividerColor,
                shape = MaterialTheme.shapes.large,
            )
            .clickableThrottleFirst(onClick = onActivityClick),
    ) {
        Column(
            verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            modifier = Modifier.padding(MaterialTheme.spacing.medium),
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.all_activities_title),
                    style = MaterialTheme.typography.bodyXLargeBold,
                    color = MaterialTheme.colorScheme.onSurface
                )
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
                ) {
                    Text(
                        text = activityCount,
                        style = MaterialTheme.typography.bodyBold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Icon(
                        painter = painterResource(R.drawable.ic_right_arrow),
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurface
                    )
                }
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = MaterialTheme.spacing.small),
            ) {
                ActivityStat(
                    value = distance,
                    label = stringResource(R.string.distance),
                    modifier = Modifier.weight(1f),
                )
                ActivityStat(
                    value = days,
                    label = stringResource(R.string.activity_days),
                    modifier = Modifier.weight(1f),
                )
                ActivityStat(
                    value = duration,
                    label = stringResource(R.string.user_profile_total_duration),
                    modifier = Modifier.weight(1f),
                )
            }
        }
    }
}

@Composable
private fun ActivityStat(
    value: AnnotatedString,
    label: String,
    modifier: Modifier = Modifier
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
        horizontalAlignment = Alignment.Start,
        modifier = modifier,
    ) {
        Row(verticalAlignment = Alignment.Bottom) {
            Text(
                text = value,
                style = MaterialTheme.typography.bodyXLargeBold
            )
        }
        Text(
            text = label,
            color = MaterialTheme.colorScheme.onSurface,
            style = MaterialTheme.typography.bodySmall,
        )
    }
}

@Composable
fun TopActivitiesSection(
    modifier: Modifier = Modifier, activities: List<TopActivity> = emptyList()
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.spacing.medium)
            .border(
                width = 1.dp,
                color = MaterialTheme.colorScheme.dividerColor,
                shape = MaterialTheme.shapes.large,
            )
            .background(MaterialTheme.colorScheme.surface),
    ) {
        Column {
            Text(
                text = stringResource(R.string.top_activities),
                color = MaterialTheme.colorScheme.onSurface,
                style = MaterialTheme.typography.bodyXLargeBold,
                modifier = Modifier.padding(MaterialTheme.spacing.medium)
            )
            HorizontalDivider(
                modifier = Modifier.padding(bottom = MaterialTheme.spacing.medium),
            )
            activities.forEachIndexed { index, activity ->
                TopActivityItem(
                    modifier = Modifier.padding(
                        start = MaterialTheme.spacing.small,
                        end = MaterialTheme.spacing.medium,
                    ),
                    icon = activity.activityType.iconId,
                    duration = activity.duration.formatWidgetDurationTitle(LocalContext.current)
                        .toString(),
                    distance = TextFormatter.formatDistanceRounded(
                        activity.measurementUnit.toDistanceUnit(
                            activity.distance
                        )
                    ),
                    progress = activity.progress,
                    measurementUnit = activity.measurementUnit
                )
                if (index == activities.size - 1) {
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
                } else {
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
                }
            }
        }
    }
}

@Composable
private fun TopActivityItem(
    @DrawableRes icon: Int,
    duration: String,
    distance: String,
    progress: Float,
    measurementUnit: MeasurementUnit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(32.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        SuuntoActivityIcon(
            iconRes = icon,
            tint = MaterialTheme.colorScheme.onSurface,
            background = Color.Transparent,
            modifier = Modifier
                .size(MaterialTheme.iconSizes.large)
                .align(Alignment.CenterVertically)
        )

        Box(
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight()
                .background(color = MaterialTheme.colorScheme.surface)
                .align(Alignment.CenterVertically)
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth(progress)
                    .fillMaxHeight()
                    .background(
                        color = MaterialTheme.colorScheme.cloudyGrey, shape = RoundedCornerShape(
                            topStart = 0.dp, topEnd = 8.dp, bottomStart = 0.dp, bottomEnd = 8.dp
                        )
                    )
            )
            Text(
                text = duration,
                style = MaterialTheme.typography.bodyLarge,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier
                    .padding(
                        horizontal = MaterialTheme.spacing.small,
                    )
                    .align(Alignment.CenterStart)
            )
        }

        Text(
            text = buildString {
                append(distance)
                append(" ")
                append(stringResource(measurementUnit.distanceUnit))
            },
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            style = MaterialTheme.typography.bodyLarge.copy(textAlign = TextAlign.End),
            color = MaterialTheme.colorScheme.secondary,
            modifier = Modifier
                .width(120.dp)
                .align(Alignment.CenterVertically)
        )
    }
}

@Composable
fun FollowButton(
    followStatus: FollowStatus,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val resId = when (followStatus) {
        FollowStatus.FOLLOWING -> R.string.following
        FollowStatus.PENDING -> R.string.requested
        FollowStatus.REJECTED -> R.string.requested
        FollowStatus.UNFOLLOWING -> R.string.follow
        FollowStatus.FAILED -> R.string.follow
        FollowStatus.FRIENDS -> R.string.following
    }
    Box(
        modifier = modifier
            .height(32.dp)
            .background(MaterialTheme.colorScheme.surface)
            .border(
                width = 1.dp,
                color = MaterialTheme.colorScheme.cloudyGrey,
                shape = MaterialTheme.shapes.extraLarge
            )
            .clip(MaterialTheme.shapes.extraLarge)
            .clickable(
                onClick = onClick
            ),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier
                .padding(
                    horizontal = MaterialTheme.spacing.large,
                )
                .align(Alignment.Center)
        ) {
            if (FollowStatus.FOLLOWING == followStatus) {
                Icon(
                    painter = painterResource(R.drawable.ic_check_follow),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.nearBlack
                )
                Spacer(modifier = Modifier.width(MaterialTheme.spacing.xsmall))
            }
            Text(
                text = stringResource(resId),
                style = MaterialTheme.typography.body,
                color = MaterialTheme.colorScheme.onSurface,
            )
        }

    }
}

@Composable
fun AchievementsSection(
    isBadgeEnabled : Boolean,
    myBadgesItems : Map<String, List<MyBadgesDisplayItem>>,
    modifier: Modifier = Modifier,
    onPersonalRecordsClick: () -> Unit = {},
    onBadgesClick: () -> Unit = {},
) {
    Column(modifier = modifier.padding(horizontal = MaterialTheme.spacing.medium)) {
        Text(
            text = stringResource(R.string.achievements),
            style = MaterialTheme.typography.bodyXLargeBold,
            modifier = Modifier.padding(vertical = MaterialTheme.spacing.medium)
        )
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(MaterialTheme.colorScheme.surface)
                .clip(MaterialTheme.shapes.large)
                .border(
                    width = 1.dp,
                    color = MaterialTheme.colorScheme.dividerColor,
                    shape = MaterialTheme.shapes.large,
                )
                .clickableThrottleFirst(onClick = onPersonalRecordsClick)
        ) {
            Column(modifier = Modifier.padding(MaterialTheme.spacing.medium)) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Column {
                        Text(
                            text = stringResource(R.string.personal_records),
                            style = MaterialTheme.typography.bodyLargeBold,
                        )
                    }
                    Icon(
                        painter = painterResource(id = R.drawable.ic_right_arrow),
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurface,
                    )
                }
            }
        }
        if (isBadgeEnabled) {
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
            BudgesAchievements(
                onBudgesAchievementsClick = onBadgesClick,
                myBagesItems = myBadgesItems
            )
        }
    }
}

@Composable
fun BudgesAchievements(
    myBagesItems : Map<String, List<MyBadgesDisplayItem>>,
    modifier: Modifier = Modifier,
    onBudgesAchievementsClick: () -> Unit = {},
){
    val totalAcquiredBadges = myBagesItems.values.flatten().count { it.isAcquired }
    
    val acquiredBadgeIcons = myBagesItems.values
        .flatten()
        .filter { it.isAcquired }
        .take(5)
        .mapNotNull { it.acquiredBadgeIconUrl }
    
    Box(
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.surface)
            .clip(MaterialTheme.shapes.large)
            .border(
                width = 1.dp,
                color = MaterialTheme.colorScheme.dividerColor,
                shape = MaterialTheme.shapes.large,
            )
            .clickableThrottleFirst(onClick = onBudgesAchievementsClick)
    ){
        Column(modifier = Modifier.padding(MaterialTheme.spacing.medium)) {
            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.my_badges_icon_on_profile),
                        contentDescription = null,
                    )
                    Column {
                        Text(
                            text = stringResource(id = R.string.main_badges_screen_title),
                            style = MaterialTheme.typography.titleMedium,
                        )
                        Text(
                            text = stringResource(R.string.badges_earned, totalAcquiredBadges),
                            style = MaterialTheme.typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                        )
                    }
                }
                Icon(
                    painter = painterResource(id = R.drawable.ic_chevron_thin),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
            
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.medium))
            if (acquiredBadgeIcons.isNotEmpty()) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
                ) {
                    acquiredBadgeIcons.forEach { badgeIconUrl ->
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(badgeIconUrl)
                                .crossfade(true)
                                .build(),
                            contentDescription = "Badge icon",
                            modifier = Modifier.size(58.dp),
                            contentScale = ContentScale.Fit
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun MenuSection(
    settingMenuList: List<SettingMenuInfo>,
    modifier: Modifier = Modifier,
    onClick: (menu: SettingMenuInfo) -> Unit = {}
) {
    Column(modifier = modifier.heightIn(min = 400.dp)) {
        Spacer(modifier = Modifier.height(24.dp))
        settingMenuList.forEach { menu ->
            MenuListItem(menu, onClick)
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.xxxxlarge))
    }
}

@Composable
fun MenuListItem(
    menu: SettingMenuInfo,
    onClick: (menu: SettingMenuInfo) -> Unit,
    modifier: Modifier = Modifier,
) {
    Box(
        contentAlignment = Alignment.CenterStart,
        modifier = modifier
            .fillMaxWidth()
            .height(56.dp)
            .background(
                color = MaterialTheme.colorScheme.surface,
            )
            .clickableThrottleFirst(onClick = { onClick(menu) })
            .padding(horizontal = MaterialTheme.spacing.medium),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        ) {
            Icon(
                painter = painterResource(id = menu.icon),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.small),
            )
            Text(
                text = menu.title,
                color = MaterialTheme.colorScheme.onSurface,
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.weight(1f),
            )
            Icon(
                painter = painterResource(id = R.drawable.ic_right_arrow),
                contentDescription = null,
                tint = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

@Composable
fun PremiumSubscriptionView(
    premiumSubscriptionStatus: CurrentPremiumSubscriptionStatus,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val containerColor = if (premiumSubscriptionStatus.isSubscribed) {
        MaterialTheme.colorScheme.surface
    } else {
        MaterialTheme.colorScheme.primaryContainer
    }
    val contentColor = if (premiumSubscriptionStatus.isSubscribed) {
        MaterialTheme.colorScheme.primaryContainer
    } else {
        MaterialTheme.colorScheme.surface
    }
    val chevronColor = if (premiumSubscriptionStatus.isSubscribed) {
        MaterialTheme.colorScheme.onSurface
    } else {
        MaterialTheme.colorScheme.surface
    }
    val borderColor = if (premiumSubscriptionStatus.isSubscribed) {
        MaterialTheme.colorScheme.dividerColor
    } else {
        MaterialTheme.colorScheme.primaryContainer
    }
    val icon = if (premiumSubscriptionStatus.isSubscribed) {
        R.drawable.premium_outline
    } else {
        R.drawable.premium_fill
    }
    val title = if (premiumSubscriptionStatus.isSubscribed) {
        stringResource(R.string.premium)
    } else {
        stringResource(R.string.get_premium)
    }
    val subtitle = if (premiumSubscriptionStatus.isSubscribed) {
        val userSubscription = premiumSubscriptionStatus.userSubscription!!
        if (userSubscription.isAutoRenew) {
            when (userSubscription.length) {
                SubscriptionInfo.SubscriptionLength.YEARLY -> stringResource(R.string.one_year_auto_renew)
                SubscriptionInfo.SubscriptionLength.MONTHLY -> stringResource(R.string.one_month_auto_renew)
                else -> ""
            }
        } else {
            val daysLeft = userSubscription.daysLeft
            pluralStringResource(R.plurals.days_left, daysLeft, daysLeft)
        }
    } else if (premiumSubscriptionStatus.hasFreeTrialAvailable) {
        stringResource(R.string.start_free_trial)
    } else {
        stringResource(R.string.profile_buy_premium)
    }
    Box(
        modifier = modifier
            .fillMaxWidth()
            .clip(MaterialTheme.shapes.large)
            .background(containerColor)
            .border(
                width = 1.dp,
                color = borderColor,
                shape = MaterialTheme.shapes.large,
            )
            .clickableThrottleFirst(onClick = onClick),
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(MaterialTheme.spacing.medium)
        ) {
            Icon(
                painter = painterResource(icon),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier.size(MaterialTheme.iconSizes.small),
            )

            Column(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
                modifier = Modifier.weight(1f),
            ) {
                Text(
                    text = title,
                    color = contentColor,
                    style = MaterialTheme.typography.bodyLargeBold,
                )
                Text(
                    text = subtitle,
                    color = contentColor,
                    style = MaterialTheme.typography.body,
                )
            }

            Icon(
                painter = painterResource(id = R.drawable.ic_right_arrow),
                contentDescription = null,
                tint = chevronColor,
            )
        }
    }
}

@Composable
fun BlockedView(
    modifier: Modifier = Modifier,
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.large),
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier.padding(MaterialTheme.spacing.xxxxlarge),
    ) {
        Icon(
            painter = painterResource(R.drawable.blocked_outline),
            contentDescription = null,
            tint = Color.Unspecified,
        )
        Text(
            text = stringResource(R.string.blocked_tips),
            style = MaterialTheme.typography.body,
            color = MaterialTheme.colorScheme.secondary,
            textAlign = TextAlign.Center,
        )
    }
}

@Preview
@Composable
private fun MenuListItemPreview() {
    M3AppTheme {
        MenuListItem(
            menu = SettingMenuInfo(
                type = SettingMenuType.SETTING,
                icon = R.drawable.ic_settings,
                title = stringResource(R.string.settings)
            ),
            onClick = {}
        )
    }
}

@Preview
@Composable
private fun BlockedViewPreview() {
    M3AppTheme {
        BlockedView()
    }
}

@Preview
@Composable
private fun PremiumSubscriptionViewPreview(
    @PreviewParameter(PremiumSubscriptionProvider::class) premiumSubscriptionStatus: CurrentPremiumSubscriptionStatus
) {
    M3AppTheme {
        PremiumSubscriptionView(
            premiumSubscriptionStatus = premiumSubscriptionStatus,
            onClick = {},
        )
    }
}

private class PremiumSubscriptionProvider :
    PreviewParameterProvider<CurrentPremiumSubscriptionStatus> {
    override val values: Sequence<CurrentPremiumSubscriptionStatus> = sequenceOf(
        CurrentPremiumSubscriptionStatus(
            userSubscription = null,
            hasFreeTrialAvailable = false
        ),
        CurrentPremiumSubscriptionStatus(
            userSubscription = null,
            hasFreeTrialAvailable = true
        ),
        CurrentPremiumSubscriptionStatus(
            userSubscription = UserSubscription(
                SubscriptionInfo.SubscriptionType.ACTIVE,
                SubscriptionInfo.SubscriptionLength.YEARLY,
                35,
                true,
                System.currentTimeMillis(),
            ),
            hasFreeTrialAvailable = false
        ),
    )
}

@Preview
@Composable
private fun AchievementsSectionPreview() {
    M3AppTheme {
        AchievementsSection(
            isBadgeEnabled = false,
            myBadgesItems = emptyMap(),
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun FollowButtonPreview() {
    M3AppTheme {
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp), modifier = Modifier.padding(8.dp)
        ) {
            FollowButton(
                followStatus = FollowStatus.FOLLOWING, onClick = {})
            FollowButton(
                followStatus = FollowStatus.PENDING, onClick = {})
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun ProfileInfoPreview() {
    M3AppTheme {
        ProfileInfo(
            isCurrentUser = true,
            username = "testtesttesttesttesttesttesttesttesttest",
            description = "description",
            followersSummary = FollowCountStats(
                followersCount = 99999,
                followingCount = 1050,
            ),
            deviceTypeList = listOf(
                DeviceType(
                    icon = R.drawable.ic_watch_basic_outline, deviceName = "Watch"
                ),
                DeviceType(
                    icon = R.drawable.headphone_outline, deviceName = "Headphone"
                ),
            ),
            friend = Friend(
                username = "xxx",
                realName = "xxx",
                profileDescription = "xxx",
                profileImageUrl = "xxx",
                friendStatus = FriendStatus.FOLLOW,
            )
        )
    }
}

@Preview
@Composable
private fun ActivityStatsSectionPreview() {
    M3AppTheme {
        ActivityStatsSection(
            MeasurementUnit.METRIC,
            workoutSummaryStats = WorkoutSummaryStats(
                totalDays = 3333,
                totalTimeSum = 333333.33,
                totalDistanceSum = 333333333.33,
                totalNumberOfWorkoutsSum = 33333333,
                activityTypeStats = listOf(),
            )
        )
    }
}

@Preview
@Composable
private fun TopActivitiesSectionPreview() {
    M3AppTheme {
        TopActivitiesSection(
            activities = listOf(
                TopActivity(
                    activityType = ActivityType.RUNNING,
                    duration = 2222.0,
                    distance = 3333.0,
                    progress = 1f,
                ),
                TopActivity(
                    activityType = ActivityType.RUNNING,
                    duration = 2222.0,
                    distance = 3333.0,
                    progress = 0.6f,
                ),
                TopActivity(
                    activityType = ActivityType.RUNNING,
                    duration = 2222.0,
                    distance = 3333.0,
                    progress = 0.2f,
                ),
            )
        )
    }
}


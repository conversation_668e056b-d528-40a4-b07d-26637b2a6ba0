package com.stt.android.social.userprofileV2.ui

import android.annotation.SuppressLint
import android.content.Intent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalWindowInfo
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.lerp
import androidx.compose.ui.zIndex
import androidx.core.graphics.drawable.toDrawable
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import coil3.request.transformations
import coil3.transform.CircleCropTransformation
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import com.stt.android.R
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.nearBlack
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.domain.user.User
import com.stt.android.social.userprofileV2.MoreMenu
import com.stt.android.social.userprofileV2.usecase.CropProfilePictureParams
import com.stt.android.social.userprofileV2.usecase.rememberClickProfilePictureHandler
import java.io.File
import com.stt.android.core.R as CoreR

private val expandedHeaderWithoutStatusBarHeight = 154.dp
private val collapsedHeaderWithoutStatusBarHeight = 52.dp
private val maxAvatarSize = 98.dp
private val minAvatarSize = 36.dp

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun ProfileHeader(
    user: User,
    location: String,
    scrollState: LazyListState,
    modifier: Modifier = Modifier,
    coverPhotoResId: Int? = R.drawable.profile_cove_photo_default,
    isShowBack: Boolean = true,
    isPremium: Boolean = false,
    isCurrentUser: Boolean = false,
    onEditClick: () -> Unit = {},
    onBackClick: () -> Unit = {},
    moreMenuList: List<MoreMenu>? = null,
    onMoreMenuItemClick: (MoreMenu) -> Unit = {},
    onUpdateProfilePictureClicked: ((Intent) -> Unit)? = null,
    tempProfilePictureFile: File? = null,
    onAvatarClick: (() -> Unit)? = null,
    content: @Composable (paddingTop: Dp, contentPaddingTop: Dp) -> Unit
) {
    val density = LocalDensity.current
    val statusBarHeight = WindowInsets.systemBars.getTop(density)

    val expandedHeight =
        with(density) { (statusBarHeight + expandedHeaderWithoutStatusBarHeight.toPx()).toDp() }
    val collapsedHeight =
        with(density) { (statusBarHeight + collapsedHeaderWithoutStatusBarHeight.toPx()).toDp() }

    val scrollProgress by remember {
        derivedStateOf {
            val layoutInfo = scrollState.layoutInfo
            val visibleItems = layoutInfo.visibleItemsInfo

            if (visibleItems.isEmpty()) {
                if (layoutInfo.totalItemsCount == 0) 0f else 1f
            } else {
                val firstVisibleItem = visibleItems.first()
                val firstItemIndex = firstVisibleItem.index
                val firstItemOffset = firstVisibleItem.offset

                val estimatedItemHeight = if (firstVisibleItem.size > 0) {
                    firstVisibleItem.size.toFloat()
                } else {
                    with(density) { 100.dp.toPx() }
                }

                val totalScrollOffset = firstItemIndex * estimatedItemHeight + (-firstItemOffset)

                val maxScroll = with(density) { expandedHeaderWithoutStatusBarHeight.toPx() }
                (totalScrollOffset / maxScroll).coerceIn(0f, 1f)
            }
        }
    }

    val backgroundColor = MaterialTheme.colorScheme.surface.copy(alpha = scrollProgress)
    val useDarkIcons = scrollProgress > 0.8f

    val systemUiController = rememberSystemUiController()
    SideEffect {
        systemUiController.setStatusBarColor(
            color = Color.Transparent,
            darkIcons = useDarkIcons,
        )
    }
    val avatarSize = lerp(maxAvatarSize, minAvatarSize, scrollProgress)

    val maxAvatarOffsetY = expandedHeight - maxAvatarSize / 2
    val minAvatarOffsetY = collapsedHeight - minAvatarSize - MaterialTheme.spacing.small
    val avatarOffsetY = lerp(maxAvatarOffsetY, minAvatarOffsetY, scrollProgress)

    val imageAlpha = 1f - scrollProgress
    val gradientAlpha = 1f - scrollProgress

    BoxWithConstraints(
        modifier = modifier.fillMaxSize()
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(expandedHeight)
                .background(backgroundColor)
        ) {
            BackgroundImage(
                user = user,
                coverPhotoResId = coverPhotoResId,
                alpha = imageAlpha,
            )
            TopGradient(
                statusBarHeight = statusBarHeight,
                alpha = gradientAlpha,
            )
        }

        content(collapsedHeight, expandedHeaderWithoutStatusBarHeight)

        HeaderTopBar(
            location = location,
            isShowBack = isShowBack,
            isCurrentUser = isCurrentUser,
            useDarkIcons = useDarkIcons,
            onBackClick = onBackClick,
            onEditClick = onEditClick,
            moreMenuList = moreMenuList,
            onMoreMenuItemClick = onMoreMenuItemClick,
            scrollProgress = scrollProgress,
        )

        FloatingAvatar(
            user = user,
            isPremium = isPremium,
            size = avatarSize,
            offsetY = avatarOffsetY,
            borderColor = if (useDarkIcons) {
                MaterialTheme.colorScheme.nearWhite
            } else {
                MaterialTheme.colorScheme.surface
            },
            isCurrentUser = isCurrentUser,
            onUpdateProfilePictureClicked = onUpdateProfilePictureClicked,
            tempProfilePictureFile = tempProfilePictureFile,
            onAvatarClick = onAvatarClick,
        )
    }
}

@Composable
private fun BackgroundImage(
    user: User,
    coverPhotoResId: Int?,
    alpha: Float,
    modifier: Modifier = Modifier,
) {
    val imageData = if (user.coverImageUrl.isNullOrEmpty() && coverPhotoResId != null) {
        coverPhotoResId
    } else {
        user.coverImageUrl
    }

    AsyncImage(
        model = ImageRequest.Builder(LocalContext.current)
            .data(imageData)
            .crossfade(true)
            .apply {
                if (coverPhotoResId != null && imageData != coverPhotoResId) {
                    placeholderWithFallback(
                        LocalContext.current, coverPhotoResId
                    )
                } else {
                    val color = MaterialTheme.colorScheme.dividerColor
                    val colorDrawable = color.toArgb().toDrawable()
                    placeholderWithFallback(
                        colorDrawable
                    )
                }
            }
            .build(),
        contentDescription = null,
        modifier = modifier
            .fillMaxSize()
            .graphicsLayer {
                this.alpha = alpha
            },
        contentScale = ContentScale.Crop,
    )
}

@Composable
private fun TopGradient(
    statusBarHeight: Int,
    alpha: Float,
    modifier: Modifier = Modifier,
) {
    val density = LocalDensity.current
    val gradientHeight =
        with(density) { (statusBarHeight + collapsedHeaderWithoutStatusBarHeight.toPx()).toDp() }

    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(gradientHeight)
            .graphicsLayer {
                this.alpha = alpha
            }
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        MaterialTheme.colorScheme.nearBlack.copy(alpha = 0.45f),
                        Color.Transparent,
                    )
                )
            )
    )
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun HeaderTopBar(
    location: String,
    isShowBack: Boolean,
    isCurrentUser: Boolean,
    useDarkIcons: Boolean,
    scrollProgress: Float,
    onBackClick: () -> Unit,
    onEditClick: () -> Unit,
    moreMenuList: List<MoreMenu>?,
    onMoreMenuItemClick: (MoreMenu) -> Unit,
    modifier: Modifier = Modifier,
) {
    var menuExpanded by remember { mutableStateOf(false) }
    val backgroundColor = Color.White.copy(alpha = scrollProgress)
    TopAppBar(
        expandedHeight = 52.dp,
        colors = TopAppBarDefaults.topAppBarColors(backgroundColor),
        title = {
            // Hide location tag for now
        },
        navigationIcon = {
            if (isShowBack) {
                SuuntoIconButton(
                    icon = SuuntoIcons.ActionBack,
                    onClick = onBackClick,
                    contentDescription = stringResource(R.string.back),
                    tint = if (useDarkIcons) {
                        MaterialTheme.colorScheme.onSurface
                    } else {
                        MaterialTheme.colorScheme.surface
                    },
                )
            }
        },
        actions = {
            ProfileTopBarAction(
                isEdit = isCurrentUser,
                onEditClick = onEditClick,
                moreMenuList = moreMenuList,
                menuExpanded = menuExpanded,
                onMenuExpanded = { menuExpanded = it },
                onMoreMenuItemClick = onMoreMenuItemClick,
                modifier = Modifier.padding(end = MaterialTheme.spacing.smaller),
            )
        },
        modifier = modifier.fillMaxWidth(),
    )
}

@Composable
private fun ProfileTopBarAction(
    isEdit: Boolean,
    onEditClick: () -> Unit,
    moreMenuList: List<MoreMenu>?,
    menuExpanded: Boolean,
    onMenuExpanded: (Boolean) -> Unit,
    onMoreMenuItemClick: (MoreMenu) -> Unit,
    modifier: Modifier = Modifier,
) {
    val screenWidthDp = with(LocalDensity.current) {
        LocalWindowInfo.current.containerSize.width.toDp()
    }
    Surface(
        shape = CircleShape,
        shadowElevation = 8.dp,
        color = MaterialTheme.colorScheme.surface.copy(alpha = 0.6f),
        modifier = modifier,
    ) {
        if (isEdit) {
            IconButton(
                onClick = onEditClick,
                modifier = Modifier.size(MaterialTheme.iconSizes.small),
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_svg_edit),
                    tint = Color.Unspecified,
                    contentDescription = null,
                )
            }
        } else if (!moreMenuList.isNullOrEmpty()) {
            IconButton(
                onClick = { onMenuExpanded(true) },
                modifier = Modifier.size(MaterialTheme.iconSizes.small),
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_svg_more),
                    tint = Color.Unspecified,
                    contentDescription = null,
                )
            }
            DropdownMenu(
                expanded = menuExpanded,
                onDismissRequest = { onMenuExpanded(false) },
                containerColor = MaterialTheme.colorScheme.surface,
                offset = DpOffset(screenWidthDp, MaterialTheme.spacing.small),
            ) {
                moreMenuList.forEach { menu ->
                    DropdownMenuItem(
                        text = {
                            Text(
                                text = stringResource(menu.label),
                                style = MaterialTheme.typography.bodyLarge,
                            )
                        },
                        onClick = {
                            onMenuExpanded(false)
                            onMoreMenuItemClick(menu)
                        }
                    )
                }
            }
        }
    }
}

@Composable
private fun FloatingAvatar(
    user: User,
    isPremium: Boolean,
    size: Dp,
    offsetY: Dp,
    borderColor: Color,
    modifier: Modifier = Modifier,
    isCurrentUser: Boolean = false,
    onUpdateProfilePictureClicked: ((Intent) -> Unit)? = null,
    tempProfilePictureFile: File? = null,
    onAvatarClick: (() -> Unit)? = null,
) {
    val avatarEditClick =
        if (isCurrentUser && onUpdateProfilePictureClicked != null && tempProfilePictureFile != null) {
            rememberClickProfilePictureHandler(
                onUpdateProfilePictureClicked,
                tempProfilePictureFile,
                CropProfilePictureParams.CLICK_AVATAR,
            )
        } else null

    Box(
        modifier = modifier
            .fillMaxWidth()
            .zIndex(10f)
    ) {
        ProfileAvatar(
            user = user,
            size = size,
            borderWidth = 2.dp,
            borderColor = borderColor,
            onClick = if (isCurrentUser) avatarEditClick else onAvatarClick,
            modifier = Modifier
                .align(Alignment.TopCenter)
                .offset(y = offsetY)
        )
        if (isPremium) {
            Icon(
                painter = painterResource(R.drawable.profile_avatar_premium),
                contentDescription = null,
                tint = Color.Unspecified,
                modifier = Modifier
                    .size(size * 36 / 96f)
                    .align(Alignment.BottomCenter)
                    .offset(
                        x = size * 0.43f,
                        y = offsetY - 2.dp
                    ),
            )
        }
    }
}

@Composable
private fun ProfileAvatar(
    user: User,
    size: Dp,
    borderWidth: Dp,
    borderColor: Color,
    modifier: Modifier = Modifier,
    onClick: (() -> Unit)? = null,
) {
    Box(
        modifier = modifier
            .size(size)
            .clip(CircleShape)
            .background(borderColor)
            .padding(borderWidth)
            .clickableThrottleFirst(enabled = onClick != null, onClick = onClick ?: {})
    ) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current).data(user.fullSizeProfileImageUrl)
                .crossfade(true).placeholderWithFallback(
                    LocalContext.current,
                    CoreR.drawable.ic_default_profile_image_light
                ).transformations(CircleCropTransformation()).build(),
            contentDescription = null,
            modifier = Modifier
                .fillMaxSize()
                .clip(CircleShape),
            contentScale = ContentScale.Crop,
        )
    }
}

@Preview(backgroundColor = 0xFFFF00, showBackground = true)
@Composable
private fun HeaderTopBarPreview() {
    M3AppTheme {
        HeaderTopBar(
            location = "China",
            isShowBack = true,
            isCurrentUser = true,
            useDarkIcons = true,
            scrollProgress = 0f,
            onBackClick = {},
            onEditClick = {},
            moreMenuList = MoreMenu.entries,
            onMoreMenuItemClick = {}
        )
    }
}

@Preview
@Composable
private fun FloatingAvatarPreview() {
    M3AppTheme {
        FloatingAvatar(
            user = User.ANONYMOUS,
            isPremium = true,
            size = 98.dp,
            offsetY = 0.dp,
            borderColor = MaterialTheme.colorScheme.surface,
        )
    }
}

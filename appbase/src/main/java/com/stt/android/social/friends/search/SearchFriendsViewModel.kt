package com.stt.android.social.friends.search

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.eventtracking.EventTracker
import com.stt.android.home.people.PeopleController
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.usecase.FollowFriendUseCase
import com.stt.android.social.friends.utils.toFriend
import com.stt.android.utils.awaitFirstNonNull
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.receiveAsFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@OptIn(ExperimentalCoroutinesApi::class, FlowPreview::class)
@HiltViewModel
class SearchFriendsViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    private val peopleController: PeopleController,
    private val followFriendUseCase: FollowFriendUseCase,
    private val eventTracker: EventTracker,
    coroutinesDispatchers: CoroutinesDispatchers,
) : ViewModel() {

    private val trackPageName = savedStateHandle.get<String>(AnalyticsEventProperty.PAGE_NAME) ?: ""

    private val _keywordFlow = MutableSharedFlow<String>(extraBufferCapacity = 1)

    private val _searchFriendsState = MutableStateFlow(
        SearchFriendsState(
            keyword = "",
            searching = false,
            searchedFriends = emptyList()
        )
    )
    val searchFriendsState: StateFlow<SearchFriendsState> = _searchFriendsState.asStateFlow()

    private val _eventChannel = Channel<SearchFriendsEvent>()
    val eventFlow = _eventChannel.receiveAsFlow()

    init {
        _keywordFlow
            .filter { it.trim().length >= MIN_SEARCH_FRIENDS_KEYWORD_LENGTH }
            .distinctUntilChanged()
            .debounce(DELAY_FOR_SEARCH)
            .flatMapLatest { keyword ->
                eventTracker.trackEvent(
                    AnalyticsEvent.SEARCH_REQUEST, mapOf(
                        AnalyticsEventProperty.PAGE_NAME to trackPageName,
                        AnalyticsEventProperty.SEARCH_WORD to keyword,
                    )
                )
                flow {
                    emit(
                        _searchFriendsState.value.copy(
                            searching = true,
                        )
                    )
                    runSuspendCatching {
                        val ufsList = peopleController.fetchUserFollowStatusesWithQuery(keyword)
                            .awaitFirstNonNull()
                        emit(
                            _searchFriendsState.value.copy(
                                searching = false,
                                searchedFriends = ufsList.map { it.toFriend() },
                            )
                        )
                    }.onFailure { e ->
                        Timber.w(e, "Failed to search friends")
                        errorTips(e)
                        emit(
                            _searchFriendsState.value.copy(
                                searching = false,
                                searchedFriends = emptyList(),
                            )
                        )
                    }
                }.flowOn(coroutinesDispatchers.io)
            }
            .onEach { state ->
                _searchFriendsState.update { state }
            }
            .launchIn(viewModelScope)
    }

    fun updateKeyword(keyword: String) {
        _searchFriendsState.update {
            it.copy(
                keyword = keyword,
            )
        }
        if (keyword.trim().length < MIN_SEARCH_FRIENDS_KEYWORD_LENGTH) {
            _searchFriendsState.update {
                it.copy(
                    searching = false,
                )
            }
        }
        _keywordFlow.tryEmit(keyword)
    }

    fun onStatusClick(friend: Friend) {
        viewModelScope.launch {
            val updated = runSuspendCatching {
                followFriendUseCase(friend)
            }.onFailure {
                errorTips(it)
                Timber.w(it, "follow/unfollow ${friend.username} failed.")
            }.getOrNull() ?: return@launch

            _searchFriendsState.update { currentState ->
                currentState.copy(
                    searchedFriends = currentState.searchedFriends.map {
                        if (it.username == updated.username) updated else it
                    }
                )
            }
        }
    }

    private fun errorTips(throwable: Throwable) {
        _eventChannel.trySend(SearchFriendsEvent.SearchFriendsError(throwable))
    }

    private companion object {
        private const val DELAY_FOR_SEARCH = 500L
    }
}

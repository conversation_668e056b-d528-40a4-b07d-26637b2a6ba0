package com.stt.android.social.badges.badgesList

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.coroutines.runSuspendCatching
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
internal class BadgesListViewModel @Inject constructor(
    private val badgesDataLoader: BadgesListDataLoader
) : ViewModel() {

    private val _uiState = MutableStateFlow<BadgesListViewData>(BadgesListViewData.Initial)
    val uiState: StateFlow<BadgesListViewData> = _uiState.asStateFlow()

    fun loadBadges(moduleName: String) {
        viewModelScope.launch {
            runSuspendCatching {
                val badgesList = badgesDataLoader.loadBadgesList(moduleName)
                _uiState.value = BadgesListViewData.Loaded(
                    moduleName = moduleName,
                    badgesList = badgesList
                )
            }.onFailure {
                Timber.w(it, "Fail to load badges list.")
                _uiState.value = BadgesListViewData.Initial
            }
        }
    }
}

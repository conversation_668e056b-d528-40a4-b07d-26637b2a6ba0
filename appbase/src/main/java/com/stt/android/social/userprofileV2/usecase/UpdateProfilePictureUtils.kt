package com.stt.android.social.userprofileV2.usecase

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.provider.MediaStore
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.StringRes
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import com.stt.android.R
import com.stt.android.multimedia.MediaStoreUtils
import com.stt.android.ui.crop.CropImageActivity
import timber.log.Timber
import java.io.File

@Composable
fun rememberClickProfilePictureHandler(
    onUpdateProfilePictureClicked: (Intent) -> Unit,
    tempProfilePictureFile: File,
    cropProfilePictureParams: CropProfilePictureParams,
): () -> Unit {
    val context = LocalContext.current

    val launcherCrop = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.let(onUpdateProfilePictureClicked)
        }
    }
    val cropButtonTitle = stringResource(cropProfilePictureParams.cropButtonTitle)
    val launcherImageSelect = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                val cropIntent = CropImageActivity.newStartIntent(
                    context = context,
                    uri = uri,
                    width = cropProfilePictureParams.width,
                    height = cropProfilePictureParams.height,
                    isOval = cropProfilePictureParams.isOval,
                    outFile = tempProfilePictureFile,
                    cropButtonTitle = cropButtonTitle,
                )
                launcherCrop.launch(cropIntent)
            }
        }
    }

    return remember(cropProfilePictureParams, tempProfilePictureFile) {
        {
            val selectIntent = buildImageSelectIntent(
                width = cropProfilePictureParams.width,
                height = cropProfilePictureParams.height,
                aspectX = cropProfilePictureParams.aspectX,
                aspectY = cropProfilePictureParams.aspectY,
                tempProfilePictureFile = tempProfilePictureFile
            )
            runCatching {
                launcherImageSelect.launch(selectIntent)
            }.onFailure { e ->
                Timber.w(e, "Failed to launch image select intent")
            }
        }
    }
}

private fun buildImageSelectIntent(
    width: Int,
    height: Int,
    aspectX: Int,
    aspectY: Int,
    tempProfilePictureFile: File,
): Intent {
    return Intent(Intent.ACTION_GET_CONTENT)
        .addCategory(Intent.CATEGORY_OPENABLE)
        .addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_PERSISTABLE_URI_PERMISSION)
        .putExtra("aspectX", aspectX)
        .putExtra("aspectY", aspectY)
        .putExtra("outputX", width)
        .putExtra("outputY", height)
        .putExtra(MediaStore.EXTRA_OUTPUT, Uri.fromFile(tempProfilePictureFile))
        .putExtra(Intent.EXTRA_MIME_TYPES, MediaStoreUtils.SUPPORTED_IMAGE_MIME_TYPES)
        .setType("image/*")
}

data class CropProfilePictureParams(
    val width: Int,
    val height: Int,
    val aspectX: Int,
    val aspectY: Int,
    val isOval: Boolean,
    @StringRes val cropButtonTitle: Int,
) {
    companion object {
        private const val PROFILE_IMAGE_WIDTH = 512
        private const val PROFILE_IMAGE_HEIGHT = 512
        private const val COVER_IMAGE_WIDTH = 1080
        private const val COVER_IMAGE_HEIGHT = 400
        val CLICK_AVATAR = CropProfilePictureParams(
            width = PROFILE_IMAGE_WIDTH,
            height = PROFILE_IMAGE_HEIGHT,
            aspectX = 1,
            aspectY = 1,
            isOval = true,
            cropButtonTitle = R.string.crop_avatar_button,
        )
        val CLICK_COVER = CropProfilePictureParams(
            width = COVER_IMAGE_WIDTH,
            height = COVER_IMAGE_HEIGHT,
            aspectX = 2,
            aspectY = 4,
            isOval = false,
            cropButtonTitle = R.string.crop_cover_button,
        )
    }
}

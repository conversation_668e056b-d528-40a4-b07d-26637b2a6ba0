package com.stt.android.social.badges.myBadgesList

internal sealed interface MyBadgesListViewData {
    data object Initial : MyBadgesListViewData

    data class Loaded(
        val myBadgesList: Map<String, List<MyBadgesDisplayItem>>
    ) : MyBadgesListViewData
}

data class MyBadgesDisplayItem(
    val isAcquired: Boolean,
    val moduleName: String,
    val badgeName: String?,
    val badgeConfigId: String,
    val badgeIconUrl: String?,
    val acquiredBadgeIconUrl: String?,
    val badgeBackgroundImageUrl: String?
)

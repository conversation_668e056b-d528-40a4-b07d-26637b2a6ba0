package com.stt.android.social.userprofileV2.ui

import android.content.Intent
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.background
import java.io.File
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.zIndex
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.stt.android.R
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.User
import com.stt.android.domain.user.subscription.CurrentPremiumSubscriptionStatus
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.social.badges.myBadgesList.MyBadgesDisplayItem
import com.stt.android.social.friends.Friend
import com.stt.android.social.userprofileV2.DeviceType
import com.stt.android.social.userprofileV2.FollowCountStats
import com.stt.android.social.userprofileV2.MoreMenu
import com.stt.android.social.userprofileV2.SettingMenuInfo
import com.stt.android.social.userprofileV2.SettingMenuType
import com.stt.android.social.userprofileV2.WorkoutSummaryStats
import com.stt.android.social.workoutlist.AllWorkoutViewModel
import com.stt.android.social.workoutlist.ui.PostViewerScreen
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler
import com.stt.android.social.workoutlist.ui.WorkoutListTab
import com.stt.android.utils.LocaleUtils

@Composable
internal fun UserProfileContent(
    user: User,
    isCurrentUser: Boolean,
    measurementUnit: MeasurementUnit,
    premiumSubscriptionStatus: CurrentPremiumSubscriptionStatus?,
    allWorkoutViewState: AllWorkoutViewModel.ViewData,
    followCountSummary: FollowCountStats?,
    userWorkoutSummaryState: WorkoutSummaryStats?,
    deviceTypeList: List<DeviceType>?,
    moreMenuList: List<MoreMenu>?,
    myBadgesItems : Map<String, List<MyBadgesDisplayItem>>,
    isBadgeEnabled : Boolean,
    workoutCardActionsHandler: WorkoutCardActionsHandler,
    modifier: Modifier = Modifier,
    settingMenuList: List<SettingMenuInfo>? = null,
    onBackClick: () -> Unit = {},
    onEditClick: () -> Unit = {},
    onAllActivityClick: () -> Unit = {},
    onPersonalRecordsClick: () -> Unit = {},
    onFollowersClicked: (Int) -> Unit = {},
    onFollowingClicked: (Int) -> Unit = {},
    onFollowButtonClicked: () -> Unit = {},
    onBadgesClick: () -> Unit = {},
    onMenuClick: (SettingMenuInfo) -> Unit = {},
    openWorkout: (WorkoutHeader, String) -> Unit = {_, _ ->},
    onMoreMenuItemClick: (MoreMenu) -> Unit = {},
    onPremiumSubscriptionClick: () -> Unit = {},
    onRetryClicked: () -> Unit = {},
    onSearchClick: () -> Unit = {},
    friend: Friend? = null,
    blocked: Boolean = false,
    onUpdateProfilePictureClicked: ((Intent) -> Unit)? = null,
    tempProfilePictureFile: File? = null,
    uploadingAvatar: Boolean = false,
) {
    val lazyListState = rememberLazyListState()
    val context = LocalContext.current

    val location = remember(user.country, user.showLocale) {
        user.country?.takeIf { isCurrentUser || user.showLocale == true }?.let { country ->
            LocaleUtils.fromCountryCode(country)
                ?.let { LocaleUtils.getDisplayCountry(it, context) }
        } ?: ""
    }

    var selectedPostIndex by remember { mutableStateOf<Int?>(null) }
    var showFullAvatar by remember { mutableStateOf(false) }

    val isUserValid = user.username.isNotEmpty()

    val posts = (allWorkoutViewState as? AllWorkoutViewModel.ViewData.Loaded)?.posts
        ?: emptyList()
    LaunchedEffect(posts) {
        if (posts.none()) {
            selectedPostIndex = null
        }
    }

    Box(
        modifier = modifier
            .fillMaxSize()
            .narrowContentWithBgColors(
                backgroundColor = MaterialTheme.colorScheme.surface,
                outerBackgroundColor = MaterialTheme.colorScheme.background
            )
    ) {
        ProfileHeader(
            user = user,
            location = location,
            scrollState = lazyListState,
            coverPhotoResId = if (isCurrentUser) {
                R.drawable.profile_cove_photo_default
            } else if (isUserValid) {
                R.drawable.profile_cove_photo_friend_default
            } else null,
            onBackClick = onBackClick,
            onEditClick = onEditClick,
            isPremium = premiumSubscriptionStatus?.isSubscribed == true,
            isCurrentUser = isCurrentUser,
            moreMenuList = moreMenuList,
            onMoreMenuItemClick = onMoreMenuItemClick,
            onUpdateProfilePictureClicked = onUpdateProfilePictureClicked,
            tempProfilePictureFile = tempProfilePictureFile,
            onAvatarClick = if (!isCurrentUser && !user.fullSizeProfileImageUrl.isNullOrBlank()) {
                { showFullAvatar = true }
            } else null,
        ) { paddingTop, contentPaddingTop ->
            LazyColumn(
                state = lazyListState,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = paddingTop),
                contentPadding = PaddingValues(
                    top = contentPaddingTop,
                ),
            ) {
                item(key = "profile_info_${user.username}") {
                    ProfileInfo(
                        isCurrentUser = isCurrentUser,
                        username = user.realName ?: user.username,
                        description = user.description,
                        followersSummary = followCountSummary,
                        deviceTypeList = deviceTypeList.takeIf { isUserValid },
                        onFollowersClicked = onFollowersClicked,
                        onFollowingClicked = onFollowingClicked,
                        onFollowButtonClicked = onFollowButtonClicked,
                        friend = friend,
                        blocked = blocked,
                    )
                    Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
                }
                if (isCurrentUser) {
                    premiumSubscriptionStatus?.let {
                        item(key = "premium_subscription_${user.username}") {
                            PremiumSubscriptionView(
                                premiumSubscriptionStatus = premiumSubscriptionStatus,
                                onClick = onPremiumSubscriptionClick,
                                modifier = Modifier
                                    .padding(bottom = MaterialTheme.spacing.medium)
                                    .padding(horizontal = MaterialTheme.spacing.medium),
                            )
                        }
                    }
                    item(key = "activity_stats_${user.username}") {
                        ActivityStatsSection(
                            unit = measurementUnit,
                            workoutSummaryStats = userWorkoutSummaryState,
                            onActivityClick = onAllActivityClick
                        )
                    }
                    item(key = "achievements_${user.username}") {
                        AchievementsSection(
                            onPersonalRecordsClick = onPersonalRecordsClick,
                            isBadgeEnabled = isBadgeEnabled,
                            myBadgesItems = myBadgesItems,
                            modifier = Modifier.padding(top = MaterialTheme.spacing.medium),
                            onBadgesClick = onBadgesClick
                        )
                    }
                    item(key = "menu_section_${user.username}") {
                        if (settingMenuList?.isNotEmpty() == true) {
                            MenuSection(
                                settingMenuList = settingMenuList,
                                onClick = onMenuClick
                            )
                        } else {
                            Spacer(modifier = Modifier.height(1.dp))
                        }
                    }
                } else if (!blocked && isUserValid) {
                    if (userWorkoutSummaryState?.activityTypeStats?.isNotEmpty() == true) {
                        item(key = "top_activities_${user.username}") {
                            TopActivitiesSection(
                                activities = userWorkoutSummaryState.activityTypeStats,
                                modifier = Modifier.padding(
                                    top = MaterialTheme.spacing.smaller,
                                    bottom = MaterialTheme.spacing.medium,
                                ),
                            )
                        }
                    }
                    stickyHeader(key = "workout_list_${user.username}") {
                        val nestedScrollConnection = remember {
                            object : NestedScrollConnection {
                                override fun onPreScroll(
                                    available: Offset,
                                    source: NestedScrollSource
                                ): Offset {
                                    val canScrollForward = lazyListState.canScrollForward
                                    if (canScrollForward) {
                                        lazyListState.dispatchRawDelta(-available.y)
                                        return available
                                    }
                                    return Offset.Zero
                                }
                            }
                        }
                        WorkoutListTab(
                            viewState = allWorkoutViewState,
                            workoutCardActionsHandler = workoutCardActionsHandler,
                            workoutSummaryStats = userWorkoutSummaryState,
                            measurementUnit = measurementUnit,
                            nestedScrollConnection = nestedScrollConnection,
                            onWorkoutPostClick = { index, photo ->
                                selectedPostIndex = index
                            },
                            onRetryClicked = onRetryClicked,
                            onSearchClick = onSearchClick,
                        )
                    }
                } else if (blocked) {
                    item(key = "blocked_${user.username}") {
                        BlockedView(
                            modifier = Modifier.fillMaxWidth(),
                        )
                    }
                }
            }
        }
        selectedPostIndex?.let { index ->
            PostViewerScreen(
                workoutPosts = posts,
                initialPostIndex = index,
                modifier = Modifier.zIndex(100f),
                onBackClick = {
                    selectedPostIndex = null
                },
                onCheckActivities = {
                    openWorkout(
                        WorkoutHeader.builder()
                            .userName(it.username)
                            .key(it.workoutKey)
                            .build(),
                        AnalyticsPropertyValue.WorkoutDetailsSourceProperty.OTHERS_POSTS,
                    )
                }
            )
        }
        AnimatedVisibility(
            visible = showFullAvatar,
            enter = fadeIn(),
            exit = fadeOut(),
        ) {
            BackHandler {
                showFullAvatar = false
            }
            Box(
                modifier = Modifier
                    .clickableThrottleFirst { showFullAvatar = false }
                    .background(MaterialTheme.colorScheme.nearWhite)
                    .fillMaxSize()
                    .zIndex(100f)
            ) {
                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(user.fullSizeProfileImageUrl)
                        .crossfade(true)
                        .build(),
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Fit,
                )
            }
        }
        LoadingContent(
            isLoading = uploadingAvatar,
            color = MaterialTheme.colorScheme.primary,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun CurrentUserEmptyPreview() {
    M3AppTheme {
        UserProfileContent(
            user = User.ANONYMOUS,
            isCurrentUser = true,
            measurementUnit = MeasurementUnit.IMPERIAL,
            premiumSubscriptionStatus = null,
            allWorkoutViewState = AllWorkoutViewModel.ViewData.Loading,
            settingMenuList = listOf(
                SettingMenuInfo(
                    type = SettingMenuType.SETTING,
                    icon = R.drawable.ic_settings,
                    title = stringResource(R.string.settings)
                ),
            ),
            followCountSummary = null,
            userWorkoutSummaryState = null,
            deviceTypeList = null,
            moreMenuList = null,
            uploadingAvatar = true,
            isBadgeEnabled = false,
            onBadgesClick = {},
            myBadgesItems = emptyMap(),
            workoutCardActionsHandler = WorkoutCardActionsHandler.EMPTY,
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun OthersEmptyPreview() {
    M3AppTheme {
        UserProfileContent(
            user = User.INVALID,
            isCurrentUser = false,
            measurementUnit = MeasurementUnit.IMPERIAL,
            premiumSubscriptionStatus = null,
            allWorkoutViewState = AllWorkoutViewModel.ViewData.Loading,
            followCountSummary = null,
            userWorkoutSummaryState = null,
            deviceTypeList = null,
            moreMenuList = null,
            onBadgesClick = {},
            isBadgeEnabled = false,
            myBadgesItems = emptyMap(),
            workoutCardActionsHandler = WorkoutCardActionsHandler.EMPTY,
        )
    }
}

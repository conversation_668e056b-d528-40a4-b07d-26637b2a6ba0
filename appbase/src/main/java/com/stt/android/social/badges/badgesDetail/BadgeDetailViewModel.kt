package com.stt.android.social.badges.badgesDetail

import android.content.ContentResolver
import android.content.Context
import android.graphics.Bitmap
import androidx.compose.ui.text.AnnotatedString
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.R
import com.stt.android.controllers.CurrentUserController
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.multimedia.MediaStoreUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.util.UUID
import javax.inject.Inject

@HiltViewModel
class BadgesDetailViewModel @Inject constructor(
    @ApplicationContext private val context: Context,
    private val dataLoader: BadgeDetailDataLoader,
    private val infoModelFormatter: InfoModelFormatter,
    private val currentUserController: CurrentUserController,
) : ViewModel() {
    private val _uiState = MutableStateFlow<BadgesDetailViewData>(BadgesDetailViewData.Initial)
    val uiState: StateFlow<BadgesDetailViewData> = _uiState.asStateFlow()

    private val _user = MutableStateFlow(currentUserController.currentUser)
    val user = _user.asStateFlow()

    fun loadBadgeDetail(badgeConfigId: String?) {
        viewModelScope.launch {
            runSuspendCatching {
                val userBadge = dataLoader.loadBadgesDetailData(badgeConfigId)
                val exploreMore = dataLoader.loadExploreMoreData(badgeConfigId)
                val conditions = dataLoader.loadConditionsData(badgeConfigId)
                val achievementList = userBadge.transcriptDataFields?.mapNotNull { field ->
                    when (field) {
                        "TOTAL_ACTIVITIES" -> userBadge.activitySessions?.let {
                            BadgesAchievementData(
                                value = AnnotatedString("$it ${context.getString(R.string.settings_push_notifications_activities)}"),
                                explanation = context.getString(R.string.total_activities_badges)
                            )
                        }

                        "TOTAL_CALORIES" -> userBadge.energy?.let {
                            BadgesAchievementData(
                                value = AnnotatedString("$it ${context.getString(com.stt.android.core.R.string.kcal)}"),
                                explanation = context.getString(R.string.energy)
                            )
                        }

                        "TOTAL_WORKOUT_DAYS" -> userBadge.totalWorkoutDays?.let {
                            BadgesAchievementData(
                                value = AnnotatedString("$it ${context.getString(com.stt.android.core.R.string.days)}"),
                                explanation = context.getString(R.string.total_workoutDays_badges)
                            )
                        }

                        "TOTAL_ASCENT" -> userBadge.totalAscent?.let {
                            BadgesAchievementData(
                                value = AnnotatedString(
                                    infoModelFormatter.unit.toAltitudeUnit(it.toDouble())
                                        .toString() +
                                        context.getString(infoModelFormatter.unit.altitudeUnit)
                                ),
                                explanation = context.getString(R.string.total_activities_badges)
                            )
                        }

                        "TOTAL_WORKOUT_DURATION" -> userBadge.totalDuration?.let {
                            BadgesAchievementData(
                                value = AnnotatedString(
                                    infoModelFormatter.unit.fromDistanceUnit(it).toString() +
                                        context.getString(infoModelFormatter.unit.distanceUnit)
                                ),
                                explanation = context.getString(R.string.total_activities_badges)
                            )
                        }

                        "MAX_PACE" -> userBadge.maxPace?.let {
                            BadgesAchievementData(
                                value = AnnotatedString(
                                    infoModelFormatter.unit.toPaceUnit(it).toString() +
                                        context.getString(infoModelFormatter.unit.paceUnit)
                                ),
                                explanation = context.getString(R.string.total_activities_badges)
                            )
                        }

                        "MAX_DISTANCE" -> userBadge.maxDistance?.let {
                            BadgesAchievementData(
                                value = AnnotatedString(
                                    infoModelFormatter.unit.fromDistanceUnit(it).toString() +
                                        context.getString(infoModelFormatter.unit.distanceUnit)
                                ),
                                explanation = context.getString(R.string.total_activities_badges)
                            )
                        }

                        "MAX_CYCLING_SPEED" -> userBadge.maxCyclingSpeed?.let {
                            BadgesAchievementData(
                                value = AnnotatedString(
                                    infoModelFormatter.unit.toSpeedUnit(it.toDouble()).toString() +
                                        context.getString(infoModelFormatter.unit.speedUnit)
                                ),
                                explanation = context.getString(R.string.total_activities_badges)
                            )
                        }

                        "MAX_DIVING_DEPTH" -> userBadge.maxDivingDepth?.let {
                            BadgesAchievementData(
                                value = AnnotatedString(
                                    infoModelFormatter.unit.toAltitudeUnit(it).toString() +
                                        context.getString(infoModelFormatter.unit.altitudeUnit)
                                ),
                                explanation = context.getString(R.string.total_activities_badges)
                            )
                        }

                        "MAX_SPEED" -> userBadge.maxSpeed?.let {
                            BadgesAchievementData(
                                value = AnnotatedString(
                                    infoModelFormatter.unit.toSpeedUnit(it.toDouble()).toString() +
                                        context.getString(infoModelFormatter.unit.speedUnit)
                                ),
                                explanation = context.getString(R.string.total_activities_badges)
                            )
                        }

                        "MAX_DURATION" -> userBadge.maxDuration?.let {
                            BadgesAchievementData(
                                value = AnnotatedString(
                                    infoModelFormatter.unit.fromMillisecondsToSeconds(it)
                                        .toString() +
                                        context.getString(infoModelFormatter.unit.altitudeUnit)
                                ),
                                explanation = context.getString(R.string.total_activities_badges)
                            )
                        }

                        else -> null
                    }
                }

                _uiState.value = BadgesDetailViewData.Loaded(
                    badgesDetail = userBadge,
                    exploreMore = exploreMore,
                    conditionData = conditions,
                    badgesAchievementDataList = achievementList
                )
            }.onFailure {
                Timber.w(it, "Fail to load badges.")
                _uiState.value = BadgesDetailViewData.Error(it)
            }
        }
    }

    fun saveBadgesImageToMedia(contentResolver: ContentResolver, bitmap: Bitmap) {
        viewModelScope.launch {
            runSuspendCatching {
                val displayName = UUID.randomUUID().toString().take(8).plus(".jpg")
                MediaStoreUtils.saveMediaToMediaStore(
                    contentResolver,
                    displayName
                ) { fileDescriptor ->
                    FileOutputStream(fileDescriptor.fileDescriptor).use { outputStream ->
                        bitmap.compress(Bitmap.CompressFormat.JPEG, 80, outputStream)
                    }
                }
            }.onFailure {
                Timber.w(it, "Fail to save sharing image.")
            }
        }
    }

    fun saveBadgesImageToCache(cacheDir: File, image: Bitmap, onSavedSuccessfully: (File) -> Unit) {
        viewModelScope.launch {
            runSuspendCatching {
                val shareImageFileCacheDir = File(
                    cacheDir,
                    "badge_share"
                ).apply { mkdirs() }
                val imageFile = File(
                    shareImageFileCacheDir,
                    UUID.randomUUID().toString().plus(".jpg")
                )
                FileOutputStream(imageFile).use {
                    image.compress(Bitmap.CompressFormat.JPEG, 80, it)
                }
                imageFile
            }.onSuccess {
                onSavedSuccessfully(it)
            }.onFailure {
                Timber.w(it, "Fail to save badges image.")
            }
        }
    }
}


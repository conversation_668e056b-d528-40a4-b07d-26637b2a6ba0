package com.stt.android.social.badges.badgesbase

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.coroutines.runSuspendCatching
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
internal class BadgesViewModel @Inject constructor(
    private val badgesDataLoader: BadgesBaseDataLoader
) : ViewModel() {

    private val _uiState = MutableStateFlow<BadgesViewData>(BadgesViewData.Initial)
    val uiState: StateFlow<BadgesViewData> = _uiState.asStateFlow()

    init {
        loadBadges()
    }

    private fun loadBadges() {
        viewModelScope.launch {
            runSuspendCatching {
                val badgeListByModule = badgesDataLoader.loadBadgeListByModule()
                val myBadges = badgesDataLoader.getMyBadgesInfo()
                _uiState.value = BadgesViewData.Loaded(
                    activityBadgesList = badgeListByModule,
                    myBadgesList = myBadges
                )
            }.onFailure {
                Timber.w(it, "Fail to load badges.")
                _uiState.value = BadgesViewData.Initial
            }
        }
    }
}

package com.stt.android.social.friends.following

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.composables.EmptyView
import com.stt.android.social.friends.composables.FriendsListView

@Composable
fun FollowingContent(
    state: FollowingState,
    onFriendClick: (Friend) -> Unit,
    onStatusClick: (Friend) -> Unit,
    modifier: Modifier = Modifier,
    isOthers: Boolean = false,
) {
    FriendsListView(
        friends = state.friends,
        onFriendClick = onFriendClick,
        onStatusClick = onStatusClick,
        modifier = modifier,
        headerView = {
            if (!isOthers) {
                item(key = "list_title_${state.followingCount}") {
                    Text(
                        stringResource(R.string.my_following_prefix, state.followingCount),
                        style = MaterialTheme.typography.bodyLargeBold,
                        modifier = Modifier.padding(MaterialTheme.spacing.medium),
                    )
                }
            }
        },
        emptyView = {
            if (state.loading) {
                LoadingContent(
                    isLoading = true,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.fillMaxSize(),
                )
            } else {
                EmptyView(
                    icon = R.drawable.ic_empty_friends,
                    tips = if (isOthers) R.string.no_people_found else R.string.no_following_new,
                    modifier = Modifier.fillMaxSize(),
                )
            }
        }
    )
}

@Preview
@Composable
private fun FollowingScreenPreview() {
    M3AppTheme {
        FollowingContent(
            state = FollowingState(
                friends = emptyList(),
                loading = true,
            ),
            onFriendClick = {},
            onStatusClick = {},
        )
    }
}

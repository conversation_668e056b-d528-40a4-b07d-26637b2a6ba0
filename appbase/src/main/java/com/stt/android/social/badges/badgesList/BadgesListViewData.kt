package com.stt.android.social.badges.badgesList

internal sealed interface BadgesListViewData {
    data object Initial : BadgesListViewData

    data class Loaded(
        val moduleName: String,
        val badgesList: List<BadgesDisplayItem>
    ) : BadgesListViewData
}

data class BadgesDisplayItem(
    val isAcquired: Boolean,
    val moduleName: String,
    val badgeName: String?,
    val badgeConfigId: String,
    val badgeIconUrl: String?,
    val acquiredBadgeIconUrl: String?,
    val badgeBackgroundImageUrl: String?
)

package com.stt.android.social.badges.myBadgesList

import com.stt.android.social.badges.badgesUseCase.MyBadgesListUseCase
import javax.inject.Inject

internal class MyBadgesListDataLoader @Inject constructor(
    private val myBadgesListUseCase: MyBadgesListUseCase
) {
    suspend fun loadMyBadgesList(): Map<String, List<MyBadgesDisplayItem>> {
        val userBadgeList =
            myBadgesListUseCase.getUserBadgeList().userHasWonBadges ?: emptyList()
        val grouped = userBadgeList.associate { userBadgeModule ->
            val moduleName = userBadgeModule.moduleName
            val badgeItems = userBadgeModule.userBadges.map { badge ->
                MyBadgesDisplayItem(
                    isAcquired = true,
                    moduleName = moduleName,
                    badgeName = badge.badgeName,
                    badgeConfigId = badge.badgeConfigId,
                    badgeIconUrl = badge.badgeIconUrl,
                    acquiredBadgeIconUrl = badge.acquiredBadgeIconUrl,
                    badgeBackgroundImageUrl = badge.badgeBackgroundImageUrl
                )
            }
            moduleName to badgeItems
        }
        return grouped
    }
}

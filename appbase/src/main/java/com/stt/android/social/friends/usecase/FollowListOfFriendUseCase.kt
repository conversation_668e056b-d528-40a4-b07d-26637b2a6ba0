package com.stt.android.social.friends.usecase

import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.awaitSuspend
import com.stt.android.home.people.PeopleController
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.FriendStatus
import kotlinx.coroutines.withContext
import javax.inject.Inject

class FollowListOfFriendUseCase @Inject constructor(
    private val peopleController: PeopleController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    suspend operator fun invoke(friends: List<Friend>) {
        withContext(coroutinesDispatchers.io) {
            val ufsList = friends.filter { it.friendStatus == FriendStatus.FOLLOW }.map { friend ->
                peopleController.loadUfsFromDbForFollowUser(friend)
            }
            peopleController.followListOfPeopleWithDelay(
                ufsList, ADD_ALL_DELAY,
                AnalyticsPropertyValue.FollowSourceProperty.FIND_FB_FRIENDS
            ).awaitSuspend()
        }
    }

    private companion object {
        private const val ADD_ALL_DELAY = 2000L
    }
}

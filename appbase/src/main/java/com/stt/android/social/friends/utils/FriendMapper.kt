package com.stt.android.social.friends.utils

import com.stt.android.follow.FollowDirection
import com.stt.android.follow.FollowStatus
import com.stt.android.follow.UserFollowStatus
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.FriendStatus

fun UserFollowStatus.toFriend(currentUsername: String? = null): Friend {
    return Friend(
        username = username,
        realName = getRealNameOrUsername(),
        profileDescription = profileDescription ?: "",
        profileImageUrl = profileImageUrl ?: "",
        friendStatus = calculateFriendStatus(currentUsername),
    )
}

fun UserFollowStatus.calculateFriendStatus(currentUsername: String? = null): FriendStatus {
    if (username == currentUsername) return FriendStatus.ME
    val effectiveStatus = when (direction) {
        FollowDirection.FOLLOWING -> status
        FollowDirection.FOLLOWER -> currentUserFollowStatus
        else -> null
    }

    return when (effectiveStatus) {
        FollowStatus.FRIENDS -> FriendStatus.FRIEND
        FollowStatus.FOLLOWING -> FriendStatus.FOLLOWING
        else -> FriendStatus.FOLLOW
    }
}

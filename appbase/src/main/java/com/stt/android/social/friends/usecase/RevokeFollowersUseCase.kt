package com.stt.android.social.friends.usecase

import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.awaitSuspend
import com.stt.android.home.people.PeopleController
import com.stt.android.social.friends.Friend
import kotlinx.coroutines.withContext
import javax.inject.Inject

class RevokeFollowersUseCase @Inject constructor(
    private val peopleController: PeopleController,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    suspend operator fun invoke(friends: List<Friend>) =
        withContext(coroutinesDispatchers.io) {
            val ids = friends.map { friend -> peopleController.loadUfsFromDbForFollower(friend).id }
            peopleController.revokeFollowersByUserFollowStatusIds(ids).awaitSuspend()
        }
}

package com.stt.android.social.workoutlist

import android.content.Context
import android.content.SharedPreferences
import androidx.core.content.edit
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.R
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.controllers.BackendController
import com.stt.android.controllers.CurrentUserController
import com.stt.android.controllers.PicturesController
import com.stt.android.controllers.UserSettingsController
import com.stt.android.controllers.VideoModel
import com.stt.android.controllers.WorkoutHeaderController
import com.stt.android.controllers.loadWorkouts
import com.stt.android.controllers.workoutUpdated
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.di.UserProfilePreferences
import com.stt.android.domain.diary.toLocalDate
import com.stt.android.domain.review.isUnknownOrPassed
import com.stt.android.domain.user.GetUserByUsernameUseCase
import com.stt.android.domain.user.ImageInformation
import com.stt.android.domain.user.MeasurementUnit
import com.stt.android.domain.user.User
import com.stt.android.domain.user.VideoInformation
import com.stt.android.domain.user.follow.IsFolloweeUseCase
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.eventtracking.EventTracker
import com.stt.android.home.dashboard.card.WorkoutCardLoader
import com.stt.android.remote.UserAgent
import com.stt.android.social.userprofileV2.BaseUserProfileActivity.Companion.KEY_USER
import com.stt.android.social.userprofileV2.BaseUserProfileActivity.Companion.KEY_USER_NAME
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler
import com.stt.android.utils.STTConstants.UserProfilePreferences.KEY_ALL_WORKOUT_LAST_FILTER
import com.stt.android.utils.STTConstants.UserProfilePreferences.KEY_ALL_WORKOUT_LAST_TAB
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.last
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.time.format.DateTimeFormatter
import java.util.Locale
import javax.inject.Inject
import com.stt.android.core.R as CR

@HiltViewModel
class AllWorkoutViewModel @Inject constructor(
    savedStateHandle: SavedStateHandle,
    @ApplicationContext private val context: Context,
    @UserAgent private val userAgent: String,
    private val workoutHeaderController: WorkoutHeaderController,
    private val workoutCardLoader: WorkoutCardLoader,
    private val coroutinesDispatchers: CoroutinesDispatchers,
    private val currentUserController: CurrentUserController,
    private val picturesController: PicturesController,
    private val videoModel: VideoModel,
    private val backendController: BackendController,
    private val isFolloweeUseCase: IsFolloweeUseCase,
    private val userSettingsController: UserSettingsController,
    private val getUserByUsernameUseCase: GetUserByUsernameUseCase,
    private val eventTracker: EventTracker,
    @UserProfilePreferences private val sharedPreferences: SharedPreferences,
    val workoutCardActionsHandler: WorkoutCardActionsHandler,
) : ViewModel() {
    sealed class ViewData {
        data object Loading : ViewData()

        data class Loaded(
            val showFilterAndDate: Boolean,
            // The key is 'All' or the activity type id
            val dateAndWorkouts: Map<String, List<Any>>,
            val posts: List<WorkoutPost>,
        ) : ViewData()

        data object Error : ViewData()
    }

    private data class CombinedData(
        val workouts: Map<String, List<Any>> = emptyMap(),
        val workoutPosts: List<WorkoutPost> = emptyList(),
    )

    val username: String = savedStateHandle.get<String>(KEY_USER_NAME)
        ?: savedStateHandle.get<User>(KEY_USER)?.username
        ?: currentUserController.username

    val trackPageName: String = savedStateHandle.get<String>(AnalyticsEventProperty.PAGE_NAME) ?: ""

    private val isCurrentUser: Boolean
        get() = username == currentUserController.username

    private val _viewState: MutableStateFlow<ViewData> = MutableStateFlow(ViewData.Loading)
    val viewState: StateFlow<ViewData> = _viewState.asStateFlow()
    val measurementUnit: MeasurementUnit
        get() = userSettingsController.settings.measurementUnit

    private val _lastOperationHistoryState: MutableStateFlow<OperationHistory> =
        MutableStateFlow(
            OperationHistory(
                lastTabIndex = sharedPreferences.getInt(KEY_ALL_WORKOUT_LAST_TAB, 0),
                lastWorkoutFilter = sharedPreferences.getString(KEY_ALL_WORKOUT_LAST_FILTER, "")
                    ?: "",
            )
        )
    val lastOperationHistoryState = _lastOperationHistoryState.asStateFlow()

    private var job: Job? = null

    private val _videosFlow = MutableStateFlow<List<VideoInformation>>(emptyList())

    init {
        workoutHeaderController.workoutUpdated
            .onStart { emit(Unit) }
            .onEach { load() }
            .launchIn(viewModelScope)
    }

    private fun getUserWorkoutFlow(): Flow<Map<String, List<Any>>> = flow {
        emit(loadUserWorkout())
    }.catch { e ->
        Timber.w(e, "Failed to load workouts")
        emit(emptyMap())
    }.flowOn(coroutinesDispatchers.io)

    private fun getImagesFlow(): Flow<List<ImageInformation>> = flow {
        val local = picturesController.findByUserName(username, 100)
        emit(local.distinctByWorkoutAndKey())

        if (isCurrentUser) {
            return@flow
        }

        // Not current user, also fetch from backend.
        val session = currentUserController.session

        // backend consider since as ">="
        val since = (local.firstOrNull()?.timestamp ?: -1L) + 1L
        val remote = backendController.fetchUserPictures(session, username, since)
            .also { saveImagesIfNeeded(it) }
        emit((local + remote).distinctByWorkoutAndKey())
    }.catch { e ->
        Timber.w(e, "Unable to load images for user profile view.")
        emit(emptyList())
    }.flowOn(coroutinesDispatchers.io)

    private fun getVideos(workouts: List<WorkoutHeader>) {
        viewModelScope.launch(coroutinesDispatchers.io) {
            runSuspendCatching {
                val videos = videoModel.findByWorkoutIds(workouts.map(WorkoutHeader::id))
                    .flatMap { it.value }
                _videosFlow.update { videos }
            }.onFailure { e ->
                Timber.w(e, "Failed to load videos")
            }
        }
    }

    /**
     * Due to some unknown reasons (such as entering somebody's workout details page with
     * pictures), the workout's pictures will be saved repeatedly in the database
     */
    private fun List<ImageInformation>.distinctByWorkoutAndKey(): List<ImageInformation> {
        return distinctBy { it.workoutKey to it.key }
    }

    fun load() {
        job?.cancel()
        job = viewModelScope.launch {
            combine(
                getUserWorkoutFlow(),
                getImagesFlow(),
                _videosFlow,
            ) { workouts, images, videos ->
                val posts = (images.map { WorkoutPost(userAgent, image = it) } + videos.map { WorkoutPost(userAgent, video = it) })
                    .filter { isCurrentUser || it.reviewState.isUnknownOrPassed() }
                    .sortedByDescending { it.timestamp }
                CombinedData(workouts, posts)
            }
                .catch { e ->
                    Timber.w(e, "Failed to load combined data")
                    emit(CombinedData())
                    _viewState.value = ViewData.Error
                }
                .collect { (workouts, posts) ->
                    _viewState.update { currentState ->
                        if (currentState is ViewData.Loaded) {
                            currentState.copy(
                                dateAndWorkouts = workouts,
                                posts = posts,
                            )
                        } else {
                            ViewData.Loaded(
                                isCurrentUser,
                                workouts,
                                posts,
                            )
                        }
                    }
                }
        }
    }

    fun onTabSelected(tabIndex: Int) {
        val trackTabName = if (tabIndex == 0) "Activities" else "Photos"
        eventTracker.trackEvent(
            AnalyticsEvent.ALL_ACTIVITIES_PAGE_VIEW,
            mapOf(AnalyticsEventProperty.TAB_NAME to trackTabName),
        )
        updateLastOperationHistory(
            _lastOperationHistoryState.value.copy(
                lastTabIndex = tabIndex
            )
        )
    }

    fun onFilterSelected(filter: String) {
        updateLastOperationHistory(
            _lastOperationHistoryState.value.copy(
                lastWorkoutFilter = filter,
            )
        )
    }

    private fun updateLastOperationHistory(operationHistory: OperationHistory) {
        _lastOperationHistoryState.update { operationHistory }
        sharedPreferences.edit {
            putInt(
                KEY_ALL_WORKOUT_LAST_TAB,
                operationHistory.lastTabIndex,
            )
            putString(
                KEY_ALL_WORKOUT_LAST_FILTER,
                operationHistory.lastWorkoutFilter,
            )
        }
    }

    private suspend fun loadUserWorkout(): Map<String, List<Any>> =
        withContext(coroutinesDispatchers.io) {
            val allWorkouts = workoutHeaderController.loadWorkouts(username)
                .last()
            val user = if (isCurrentUser) {
                currentUserController.currentUser
            } else {
                runSuspendCatching {
                    getUserByUsernameUseCase.getUserByUsername(username, false)
                }.getOrElse {
                    getUserByUsernameUseCase.getUserByUsername(username, true)
                }
            }
            getVideos(allWorkouts)
            val allWorkoutCards = workoutCardLoader.buildWorkoutCards(
                userWorkoutPairs = allWorkouts.map { user to it },
                isOwnWorkout = isCurrentUser,
                includeCover = true,
            )
            if (!isCurrentUser) {
                return@withContext buildMap {
                    put(context.getString(CR.string.all_filter_tag), allWorkoutCards)
                }
            }
            val dateFormatter = DateTimeFormatter.ofPattern(
                "MMMM yyyy",
                Locale(context.getString(R.string.language_code)),
            )
            val allWorkoutsWithDates = allWorkoutCards.groupBy {
                dateFormatter.format(it.workoutHeader.startTime.toLocalDate())
            }.flatMap { (date, workouts) ->
                listOf(DateHeader(date, workouts.size)) + workouts
            }
            val groupedByActivityType = allWorkoutCards
                .groupBy { it.workoutHeader.activityType.id.toString() }
                .toList()
                .sortedByDescending { (_, workouts) -> workouts.size }
                .take(5)
                .toMap()
                .mapValues { (_, workouts) ->
                    workouts.groupBy {
                        dateFormatter.format(it.workoutHeader.startTime.toLocalDate())
                    }.flatMap { (date, workouts) ->
                        listOf(DateHeader(date, workouts.size)) + workouts
                    }
                }

            buildMap {
                put(context.getString(CR.string.all_filter_tag), allWorkoutsWithDates)
                putAll(groupedByActivityType)
            }
        }

    private suspend fun saveImagesIfNeeded(images: List<ImageInformation>) {
        if (!isFolloweeUseCase.isFollowee(username)) {
            return
        }
        runSuspendCatching {
            // i love my friends, so cache the data
            picturesController.store(images)
        }.onFailure { e ->
            Timber.w(e, "Error while storing followee images")
        }
    }
}

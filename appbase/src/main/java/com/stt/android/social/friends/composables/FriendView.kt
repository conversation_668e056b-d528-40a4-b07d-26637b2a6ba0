package com.stt.android.social.friends.composables

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Checkbox
import androidx.compose.material3.CheckboxDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import coil3.request.transformations
import coil3.size.Size
import coil3.transform.CircleCropTransformation
import com.stt.android.R
import com.stt.android.coil.placeholderWithFallback
import com.stt.android.compose.modifiers.clickableThrottleFirst
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.iconSizes
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.AutoSizeText
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.FriendStatus
import com.stt.android.social.friends.utils.titlePrefixIconRes
import com.stt.android.social.friends.utils.titleRes
import kotlin.math.roundToInt
import com.stt.android.core.R as CR

@Composable
fun FriendView(
    friend: Friend,
    onClick: () -> Unit,
    onStatusClick: () -> Unit,
    modifier: Modifier = Modifier,
    editing: Boolean = false,
    selected: Boolean = false,
) {
    Column(
        modifier = modifier,
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .fillMaxWidth()
                .clickableThrottleFirst(enabled = friend.isValid, onClick = onClick)
                .padding(
                    start = if (editing) MaterialTheme.spacing.xsmall else MaterialTheme.spacing.medium,
                    end = MaterialTheme.spacing.medium,
                )
                .padding(vertical = MaterialTheme.spacing.medium),
        ) {

            if (editing) {
                Checkbox(
                    checked = selected,
                    onCheckedChange = { onClick() },
                    colors = CheckboxDefaults.colors(
                        uncheckedColor = MaterialTheme.colorScheme.mediumGrey,
                    ),
                )
            }

            val size = LocalDensity.current.run { MaterialTheme.iconSizes.large.toPx() }.roundToInt()
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(friend.profileImageUrl)
                    .crossfade(true)
                    .size(Size(size, size))
                    .placeholderWithFallback(
                        LocalContext.current,
                        CR.drawable.ic_default_profile_image_light,
                    )
                    .transformations(CircleCropTransformation())
                    .build(),
                contentDescription = null,
                modifier = Modifier.size(MaterialTheme.iconSizes.large),
            )

            Column(
                verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xxsmall),
                modifier = Modifier
                    .weight(1f)
                    .padding(start = MaterialTheme.spacing.small),
            ) {
                Text(
                    text = friend.realName,
                    style = MaterialTheme.typography.bodyLarge,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                )

                if (friend.profileDescription.isNotBlank()) {
                    Text(
                        text = friend.profileDescription,
                        modifier = Modifier.padding(top = MaterialTheme.spacing.xxsmall),
                        style = MaterialTheme.typography.body,
                        color = MaterialTheme.colorScheme.secondary,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis,
                    )
                }
            }

            if (!editing && friend.friendStatus != FriendStatus.ME) {
                FriendStatusView(
                    friendStatus = friend.friendStatus,
                    onClick = onStatusClick,
                    modifier = Modifier.padding(start = MaterialTheme.spacing.large),
                    friendValid = friend.isValid,
                )
            }
        }

        HorizontalDivider()
    }
}

@Composable
private fun FriendStatusView(
    friendStatus: FriendStatus,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    friendValid: Boolean = true,
) {
    var showConfirmDialog by remember { mutableStateOf(false) }
    OutlinedButton(
        onClick = {
            if (friendStatus != FriendStatus.FOLLOW) {
                showConfirmDialog = true
            } else {
                onClick()
            }
        },
        shape = RoundedCornerShape(32.dp),
        border = if (friendStatus == FriendStatus.FOLLOW) {
            BorderStroke(1.dp, MaterialTheme.colorScheme.primary)
        } else null,
        colors = ButtonDefaults.buttonColors(
            containerColor = if (friendStatus == FriendStatus.FOLLOW) {
                MaterialTheme.colorScheme.surface
            } else {
                MaterialTheme.colorScheme.primary
            },
            contentColor = if (friendStatus == FriendStatus.FOLLOW) {
                MaterialTheme.colorScheme.primary
            } else {
                MaterialTheme.colorScheme.onPrimary
            },
        ),
        contentPadding = PaddingValues(horizontal = MaterialTheme.spacing.small),
        modifier = modifier
            .height(32.dp)
            .width(106.dp),
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            friendStatus.titlePrefixIconRes?.let { icon ->
                Icon(
                    painter = painterResource(icon),
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onPrimary,
                    modifier = Modifier.size(MaterialTheme.iconSizes.small),
                )
            }
            AutoSizeText(
                text = if (friendValid) {
                    stringResource(friendStatus.titleRes)
                } else {
                    stringResource(R.string.invite_friends_btn_title)
                },
                maxLines = 2,
                maxTextSize = MaterialTheme.typography.body.fontSize,
                minTextSize = 8.sp,
                style = MaterialTheme.typography.bodyMedium,
                alignment = Alignment.Center,
                fontWeight = FontWeight.Normal,
                lineSpaceRatio = 1.16f,
                overflow = TextOverflow.Ellipsis,
            )
        }
    }
    if (showConfirmDialog) {
        UnfollowConfirmDialog(
            onConfirm = {
                onClick()
                showConfirmDialog = false
            },
            onDismiss = { showConfirmDialog = false },
        )
    }
}

@Composable
fun OutlinedFriendStatusButton(
    friendStatus: FriendStatus,
    blocked: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    var showConfirmDialog by remember { mutableStateOf(false) }
    OutlinedButton(
        onClick = {
            if (blocked) {
                onClick()
            } else if (friendStatus != FriendStatus.FOLLOW) {
                showConfirmDialog = true
            } else {
                onClick()
            }
        },
        shape = RoundedCornerShape(32.dp),
        border = BorderStroke(1.dp, MaterialTheme.colorScheme.onSurface),
        colors = ButtonDefaults.buttonColors(
            containerColor = MaterialTheme.colorScheme.surface,
            contentColor = MaterialTheme.colorScheme.onSurface,
        ),
        contentPadding = PaddingValues(horizontal = MaterialTheme.spacing.small),
        modifier = modifier
            .height(32.dp)
            .defaultMinSize(minWidth = 106.dp),
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xsmall),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            if (!blocked) {
                friendStatus.titlePrefixIconRes?.let { icon ->
                    Icon(
                        painter = painterResource(icon),
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.onSurface,
                        modifier = Modifier.size(MaterialTheme.iconSizes.small),
                    )
                }
            }
            Text(
                text = if (blocked) {
                    stringResource(R.string.unBlock)
                } else {
                    stringResource(friendStatus.titleRes)
                },
                style = MaterialTheme.typography.body,
                textAlign = TextAlign.Center,
            )
        }
    }
    if (showConfirmDialog) {
        UnfollowConfirmDialog(
            onConfirm = {
                onClick()
                showConfirmDialog = false
            },
            onDismiss = { showConfirmDialog = false },
        )
    }
}

@Preview
@Preview(locale = "fr")
@Composable
private fun FriendPreview() {
    M3AppTheme {
        Column {
            FriendStatus.entries.forEachIndexed { index, friendStatus ->
                FriendView(
                    friend = Friend(
                        username = "username",
                        realName = "realName",
                        profileDescription = "this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this is profile description, this",
                        profileImageUrl = "profileImageUrl",
                        friendStatus = friendStatus,
                    ),
                    onClick = {},
                    onStatusClick = {},
                    editing = index % 2 == 0,
                    selected = index == 0,
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun FriendStatusButtonPreview() {
    M3AppTheme {
        Column {
            OutlinedFriendStatusButton(FriendStatus.FOLLOW, true, {})
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                modifier = Modifier.padding(8.dp)
            ) {
                FriendStatus.entries.forEach {
                    OutlinedFriendStatusButton(it, false, {})
                }
            }
        }
    }
}

@Preview
@Composable
private fun InvalidFriendPreview() {
    M3AppTheme {
        FriendView(
            Friend(username = "", realName = "Contact friend", "", "", FriendStatus.FOLLOW),
            onClick = {},
            onStatusClick = {},
        )
    }
}

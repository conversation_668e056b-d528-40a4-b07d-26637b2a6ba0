package com.stt.android.social.friends.facebook

import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.FriendStatus

data class FacebookFriendsState(
    val friends: List<Friend>,
    val addingAll: <PERSON><PERSON><PERSON>,
    val loading: <PERSON><PERSON><PERSON>,
) {
    val unfollowingFriendCount: Int = friends.count { it.friendStatus == FriendStatus.FOLLOW }
    val noFriend = friends.none()
    val noFriendToFollow = friends.none { it.friendStatus == FriendStatus.FOLLOW }
}

sealed interface FacebookFriendsEvent
sealed interface FacebookFriendsFailedEvent : FacebookFriendsEvent {
    val throwable: Throwable
}

data class FacebookFriendsFailedToLoad(override val throwable: Throwable) : FacebookFriendsFailedEvent
data class FacebookFriendsFailedToAddAll(override val throwable: Throwable) : FacebookFriendsFailedEvent
data class FacebookFriendsFailedToFollow(val friend: Friend, override val throwable: Throwable) : FacebookFriendsFailedEvent

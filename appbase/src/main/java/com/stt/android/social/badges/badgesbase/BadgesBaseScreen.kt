package com.stt.android.social.badges.badgesbase

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.foundation.Image
import androidx.compose.runtime.Composable
import androidx.compose.foundation.clickable
import androidx.compose.foundation.verticalScroll
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import coil3.compose.AsyncImage
import com.stt.android.R
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.material3.body
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.material3.bodyMegaBold
import com.stt.android.compose.theme.material3.bodyXLarge
import com.stt.android.compose.theme.spacing

@Composable
internal fun BadgesLoadedScreen(
    viewData: BadgesViewData.Loaded,
    onEvent: (BadgesViewEvent) -> Unit,
    modifier: Modifier = Modifier,
) {
    Scaffold(
        topBar = {
            BadgesTopBar(
                title = R.string.main_badges_screen_title,
                onEvent = onEvent
            )
        },
        modifier = modifier
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .narrowContentWithBgColors(
                    backgroundColor = MaterialTheme.colorScheme.surface,
                    outerBackgroundColor = MaterialTheme.colorScheme.background
                )
                .padding(paddingValues)
                .verticalScroll(rememberScrollState())
        ) {
            MyBadgesSection(
                numberOfBadges = viewData.myBadgesList.numberOfBadges,
                recentBadgeImage = viewData.myBadgesList.recentBadgeImage,
                onEvent = onEvent,
            )
            Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
            viewData.activityBadgesList.forEach { (moduleName, badgeList) ->
                ModuleBadgeRow(
                    moduleName = moduleName,
                    badges = badgeList,
                    onEvent = onEvent,
                )
                Spacer(modifier = Modifier.height(MaterialTheme.spacing.smaller))
            }
        }
    }
}

@Composable
private fun MyBadgesSection(
    numberOfBadges: String,
    recentBadgeImage: String?,
    onEvent: (BadgesViewEvent) -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(148.dp)
            .background(MaterialTheme.colorScheme.surface),
        contentAlignment = Alignment.Center
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,

            ) {
            Column(
                modifier = Modifier.padding(start = MaterialTheme.spacing.smaller),
                horizontalAlignment = Alignment.Start
            ) {
                Row(
                    modifier = Modifier
                        .padding(bottom = MaterialTheme.spacing.small)
                        .clickable { onEvent(BadgesViewEvent.OnMyListClick) },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(id = R.string.my_badges_title),
                        style = MaterialTheme.typography.bodyMegaBold,
                    )
                    Image(
                        painter = painterResource(id = R.drawable.icon_arrow_right_black),
                        contentDescription = null,
                        modifier = Modifier,
                    )
                }
                Text(
                    text = stringResource(R.string.badges_earned, numberOfBadges),
                    style = MaterialTheme.typography.bodyXLarge
                )
            }
            Box(
                modifier = Modifier
                    .padding(end = MaterialTheme.spacing.large)
                    .size(108.dp),
                contentAlignment = Alignment.Center
            ) {
                if (!recentBadgeImage.isNullOrBlank()) {
                    AsyncImage(
                        model = recentBadgeImage,
                        contentDescription = "recent badge",
                        modifier = Modifier.size(108.dp)
                    )
                }
            }
        }
    }
}

@Composable
private fun ModuleBadgeRow(
    moduleName: String,
    badges: List<BadgesDisplayItem>,
    onEvent: (BadgesViewEvent) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.spacing.medium)
                .clickable { onEvent(BadgesViewEvent.OnListClick(moduleName)) },
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = moduleName,
                style = MaterialTheme.typography.bodyLargeBold,
                modifier = Modifier
                    .padding(
                        horizontal = MaterialTheme.spacing.medium,
                        vertical = MaterialTheme.spacing.small
                    )
            )
            Image(
                painter = painterResource(id = R.drawable.icon_arrow_right_black),
                contentDescription = null,
                modifier = Modifier,
            )
        }
        Row(
            modifier = Modifier
                .horizontalScroll(rememberScrollState())
                .padding(horizontal = MaterialTheme.spacing.large),
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.xlarge)
        ) {
            badges.forEach { badge ->
                BadgeItem(
                    badge = badge,
                    onEvent = onEvent
                )
            }
        }
    }
}

@Composable
private fun BadgeItem(
    badge: BadgesDisplayItem,
    onEvent: (BadgesViewEvent) -> Unit
) {
    Column(
        modifier = Modifier
            .width(80.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val imageUrl = if (badge.isAcquired) {
            badge.acquiredBadgeIconUrl
        } else {
            badge.badgeIconUrl
        }
        Box(
            modifier = Modifier
                .size(80.dp)
                .clickable { onEvent(BadgesViewEvent.OnBadgesClick(badge.badgeConfigId)) },
            contentAlignment = Alignment.Center
        ) {
            if (!imageUrl.isNullOrBlank()) {
                AsyncImage(
                    model = imageUrl,
                    contentDescription = badge.badgeName,
                    modifier = Modifier.size(80.dp)
                )
            }
        }
        Spacer(modifier = Modifier.height(MaterialTheme.spacing.small))
        Text(
            text = badge.badgeName ?: "",
            style = MaterialTheme.typography.body,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@OptIn(androidx.compose.material3.ExperimentalMaterial3Api::class)
@Composable
internal fun BadgesTopBar(
    @StringRes title: Int,
    onEvent: (BadgesViewEvent) -> Unit,
) {
    SuuntoTopBar(
        title = stringResource(id = title),
        onNavigationClick = { onEvent(BadgesViewEvent.Close) },
    )
}

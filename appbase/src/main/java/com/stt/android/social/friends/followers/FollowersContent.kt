package com.stt.android.social.friends.followers

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.LoadingContent
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.FriendStatus
import com.stt.android.social.friends.composables.EmptyView
import com.stt.android.social.friends.composables.FriendsListView

@Composable
fun FollowersContent(
    state: FollowersState,
    onFriendClick: (Friend) -> Unit,
    onStatusClick: (Friend) -> Unit,
    onEditClick: () -> Unit,
    modifier: Modifier = Modifier,
    isOthers: Boolean = false,
) {
    FriendsListView(
        friends = state.friends,
        editing = state.editing,
        selectedFriends = state.selectedFriends,
        onFriendClick = onFriendClick,
        onStatusClick = onStatusClick,
        modifier = modifier,
        headerView = {
            if (!state.editing && !isOthers) {
                item(key = "list_title_${state.followersCount}") {
                    Row {
                        Text(
                            stringResource(R.string.my_followers_prefix, state.followersCount),
                            style = MaterialTheme.typography.bodyLargeBold,
                            modifier = Modifier
                                .weight(1f)
                                .padding(MaterialTheme.spacing.medium),
                        )
                        IconButton(onClick = onEditClick) {
                            Icon(
                                painter = painterResource(R.drawable.edit_outline_new),
                                contentDescription = null,
                            )
                        }
                    }
                }
            }
        },
        emptyView = {
            if (state.loading) {
                LoadingContent(
                    isLoading = true,
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier.fillMaxSize(),
                )
            } else {
                EmptyView(
                    icon = R.drawable.ic_empty_friends,
                    tips = if (isOthers) R.string.no_people_found else R.string.no_followers_new,
                    modifier = Modifier.fillMaxSize(),
                )
            }
        }
    )
}

@Preview
@Composable
private fun FollowersContentPreview() {
    M3AppTheme {
        FollowersContent(
            state = FollowersState(
                friends = FriendStatus.entries.map {
                    Friend(
                        username = "username",
                        realName = "realName",
                        profileDescription = "",
                        profileImageUrl = "",
                        friendStatus = it,
                    )
                },
                editing = true,
                selectedFriends = FriendStatus.entries.map {
                    Friend(
                        username = "username",
                        realName = "realName",
                        profileDescription = "",
                        profileImageUrl = "",
                        friendStatus = it,
                    )
                },
                loading = false,
            ),
            onFriendClick = {},
            onStatusClick = {},
            onEditClick = {},
        )
    }
}

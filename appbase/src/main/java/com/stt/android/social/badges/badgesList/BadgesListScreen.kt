package com.stt.android.social.badges.badgesList

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.foundation.lazy.grid.items
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.stt.android.compose.component.SuuntoTopBar
import com.stt.android.compose.modifiers.narrowContentWithBgColors
import com.stt.android.compose.theme.spacing

@Composable
internal fun BadgesListScreen(
    viewData: BadgesListViewData,
    onEvent: (BadgesListViewEvent) -> Unit,
) {
    Scaffold(
        topBar = {
            BadgesListTopBar(
                title = (viewData as? BadgesListViewData.Loaded)?.moduleName ?: "",
                onEvent = onEvent,
            )
        }
    ) { paddingValues ->
        Box(modifier = Modifier.padding(paddingValues)) {
            when (viewData) {
                is BadgesListViewData.Initial -> {
                    Box(modifier = Modifier.fillMaxSize()) {
                        CircularProgressIndicator(
                            modifier = Modifier
                                .align(Alignment.Center)
                                .size(48.dp),
                            color = MaterialTheme.colorScheme.primary,
                        )
                    }
                }

                is BadgesListViewData.Loaded -> {
                    BadgesListContent(badges = viewData.badgesList, onEvent = onEvent)
                }
            }
        }
    }
}

@Composable
internal fun BadgesListTopBar(
    title: String,
    onEvent: (BadgesListViewEvent) -> Unit,
) {
    SuuntoTopBar(
        title = title,
        onNavigationClick = { onEvent(BadgesListViewEvent.Close) },
    )
}

@Composable
private fun BadgesListContent(
    badges: List<BadgesDisplayItem>,
    onEvent: (BadgesListViewEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(3),
        modifier = modifier
            .narrowContentWithBgColors(
                backgroundColor = MaterialTheme.colorScheme.surface,
                outerBackgroundColor = MaterialTheme.colorScheme.background
            ),
        contentPadding = PaddingValues(MaterialTheme.spacing.medium),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.large)
    ) {
        items(badges) { badge ->
            BadgeItem(badge = badge , onEvent = onEvent)
        }
    }
}

@Composable
private fun BadgeItem(
    badge: BadgesDisplayItem,
    onEvent: (BadgesListViewEvent) -> Unit,
    modifier: Modifier = Modifier
) {
    val imageUrl = if (badge.isAcquired) {
        badge.acquiredBadgeIconUrl
    } else {
        badge.badgeIconUrl
    }
    val itemModifier = if (badge.isAcquired) {
        modifier
    } else {
        modifier.alpha(0.5f)
    }

    Column(
        modifier = itemModifier
            .clickable { onEvent(BadgesListViewEvent.OnListBadgesClick(badge.badgeConfigId)) },
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small)
    ) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(imageUrl)
                .crossfade(true)
                .build(),
            contentDescription = badge.badgeName,
            modifier = Modifier.size(100.dp),
            contentScale = ContentScale.Crop
        )
        badge.badgeName?.let {
            Text(
                text = it,
                style = MaterialTheme.typography.bodyMedium,
                textAlign = TextAlign.Center
            )
        }
    }
}

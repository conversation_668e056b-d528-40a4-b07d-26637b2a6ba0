package com.stt.android.social.badges.badgesDetail

import androidx.compose.ui.text.AnnotatedString
import com.stt.android.data.badges.BadgeConditionName
import com.stt.android.data.badges.ExploreMore
import com.stt.android.data.badges.UserBadge

sealed interface BadgesDetailViewData {
    data object Initial : BadgesDetailViewData

    data class Loaded(
        val badgesDetail : UserBadge,
        val exploreMore : List<ExploreMore>,
        val conditionData : List<BadgeProgressValueWithTarget>,
        val badgesAchievementDataList : List<BadgesAchievementData>?,
    ) : BadgesDetailViewData

    data class Error(val error: Throwable): BadgesDetailViewData
}

data class BadgeProgressValueWithTarget(
    val current: String,
    val target: String,
    val conditionName: BadgeConditionName?
)

data class BadgesAchievementData(
    val value: AnnotatedString,
    val explanation: String,
)

package com.stt.android.social.userprofileV2.ui

import android.content.Intent
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import com.stt.android.domain.user.User
import com.stt.android.domain.workouts.WorkoutHeader
import com.stt.android.social.userprofileV2.MoreMenu
import com.stt.android.social.userprofileV2.SettingMenuInfo
import com.stt.android.social.userprofileV2.UserProfileViewModel
import com.stt.android.social.workoutlist.AllWorkoutViewModel
import com.stt.android.ui.components.workout.actions.WorkoutCardActionsHandler
import java.io.File

@Composable
internal fun UserProfileScreen(
    viewModel: UserProfileViewModel,
    allWorkoutViewModel: AllWorkoutViewModel,
    menuList: List<SettingMenuInfo>,
    onBackClick: () -> Unit,
    onEditClick: () -> Unit,
    onAllActivityClick: (User) -> Unit,
    onPersonalRecordsClick: () -> Unit,
    onBadgesClick: () -> Unit,
    onFollowersClicked: (Int) -> Unit,
    onFollowingClicked: (Int) -> Unit,
    onFollowButtonClickedV2: () -> Unit,
    onMenuClick: (SettingMenuInfo) -> Unit,
    onPremiumSubscriptionClick: (Boolean) -> Unit,
    openWorkout: (WorkoutHeader, String) -> Unit,
    onMoreMenuItemClick: (MoreMenu) -> Unit,
    workoutCardActionsHandler: WorkoutCardActionsHandler,
    onSearchClick: () -> Unit,
    modifier: Modifier = Modifier,
    onUpdateAvatarClicked: ((Intent) -> Unit)? = null,
    tempProfilePictureFile: File? = null,
) {
    val viewState by viewModel.viewState.collectAsState()
    val allWorkoutViewState by allWorkoutViewModel.viewState.collectAsState()
    val followCountSummary by viewModel.followCountSummaryState.collectAsState()
    val userWorkoutSummaryState by viewModel.userWorkoutSummaryState.collectAsState()
    val deviceTypeList by viewModel.deviceTypeListState.collectAsState()
    val moreMenuList by viewModel.moreMenuListState.collectAsState()
    val premiumSubscriptionStatus by viewModel.premiumSubscriptionStatus.collectAsState()
    val myBadgesListItems by viewModel.myBadgesUiState.collectAsState()
    val friend = viewState.friend
    val blocked = viewState.blockStatus.isUserBlocked
    UserProfileContent(
        modifier = modifier,
        user = viewState.user,
        isCurrentUser = viewState.isCurrentUser,
        measurementUnit = viewModel.measurementUnit,
        allWorkoutViewState = allWorkoutViewState,
        followCountSummary = followCountSummary,
        userWorkoutSummaryState = userWorkoutSummaryState,
        deviceTypeList = deviceTypeList,
        moreMenuList = moreMenuList,
        myBadgesItems = myBadgesListItems,
        premiumSubscriptionStatus = premiumSubscriptionStatus,
        settingMenuList = if (viewState.isCurrentUser) menuList else null,
        onBackClick = onBackClick,
        openWorkout = openWorkout,
        onMoreMenuItemClick = onMoreMenuItemClick,
        onEditClick = onEditClick,
        onAllActivityClick = {
            onAllActivityClick(viewState.user)
        },
        onPersonalRecordsClick = onPersonalRecordsClick,
        onFollowersClicked = onFollowersClicked,
        onFollowingClicked = onFollowingClicked,
        onFollowButtonClicked = onFollowButtonClickedV2,
        onMenuClick = onMenuClick,
        onPremiumSubscriptionClick = {
            onPremiumSubscriptionClick(premiumSubscriptionStatus?.isSubscribed == true)
        },
        onRetryClicked = allWorkoutViewModel::load,
        onSearchClick = onSearchClick,
        friend = friend,
        blocked = blocked,
        onUpdateProfilePictureClicked = if (viewState.isCurrentUser) onUpdateAvatarClicked else null,
        tempProfilePictureFile = if (viewState.isCurrentUser) tempProfilePictureFile else null,
        uploadingAvatar = viewState.uploadingAvatar,
        onBadgesClick = onBadgesClick,
        isBadgeEnabled = viewState.isBadgeEnabled,
        workoutCardActionsHandler = workoutCardActionsHandler,
    )
}

package com.stt.android.social.friends.facebook

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.R
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.social.friends.Friend
import com.stt.android.social.friends.composables.FriendsListView

private val FACEBOOK_ICON_COLOR = Color(0xFF0766FE)

@Composable
fun FacebookFriendsContent(
    state: FacebookFriendsState,
    onFriendClick: (Friend) -> Unit,
    onStatusClick: (Friend) -> Unit,
    onAddAllClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    if (state.loading || state.addingAll) {
        Box(
            contentAlignment = Alignment.Center,
            modifier = modifier.fillMaxSize(),
        ) {
            CircularProgressIndicator()
        }
    } else {
        FriendsListView(
            friends = state.friends,
            onFriendClick = onFriendClick,
            onStatusClick = onStatusClick,
            modifier = modifier,
            headerView = {
                item(key = "list_title") {
                    HeaderView(
                        state = state,
                        onAddAllClick = onAddAllClick,
                    )
                }
            },
            alwaysShowHeaderView = true,
        )
    }
}

@Composable
private fun HeaderView(
    state: FacebookFriendsState,
    onAddAllClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val noFriend = state.noFriend
    val noFriendToFollow = state.noFriendToFollow
    val headerTextRes = if (noFriend) {
        R.string.fb_friends_view_empty_text
    } else {
        R.string.fb_friends_view_promote_text
    }
    val buttonTextRes = if (noFriend) {
        R.string.find_people
    } else if (noFriendToFollow) {
        R.string.fb_friends_all_added
    } else {
        R.string.fb_friends_add_all
    }

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.large),
        modifier = modifier.padding(
            top = MaterialTheme.spacing.xxlarge,
            start = MaterialTheme.spacing.medium,
            end = MaterialTheme.spacing.medium,
        ),
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Icon(
                painter = painterResource(R.drawable.facebook_fill),
                contentDescription = null,
                tint = FACEBOOK_ICON_COLOR,
                modifier = Modifier.size(96.dp),
            )
            Text(
                text = stringResource(headerTextRes),
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(top = MaterialTheme.spacing.medium),
                textAlign = TextAlign.Center,
            )
            if (noFriend) {
                Text(
                    text = stringResource(R.string.fb_friends_try_search),
                    style = MaterialTheme.typography.bodyLarge,
                    modifier = Modifier.padding(top = MaterialTheme.spacing.medium),
                )
            }
        }

        Button(
            onClick = onAddAllClick,
            enabled = noFriend || !noFriendToFollow,
            modifier = Modifier.fillMaxWidth().height(48.dp),
            shape = RoundedCornerShape(8.dp),
        ) {
            Text(
                text = stringResource(buttonTextRes).uppercase(),
            )
        }

        if (!noFriend) {
            Text(
                text = stringResource(R.string.fb_friends_list_title),
                style = MaterialTheme.typography.bodyLargeBold,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = MaterialTheme.spacing.medium),
            )
        }
    }
}

@Preview
@Composable
private fun FacebookFriendsContentPreview() {
    M3AppTheme {
        FacebookFriendsContent(
            state = FacebookFriendsState(
                friends = emptyList(),
                addingAll = false,
                loading = false,
            ),
            onFriendClick = {},
            onStatusClick = {},
            onAddAllClick = {},
        )
    }
}

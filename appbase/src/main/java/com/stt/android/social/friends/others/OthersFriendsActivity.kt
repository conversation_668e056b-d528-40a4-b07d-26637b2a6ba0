package com.stt.android.social.friends.others

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.social.friends.Friend
import com.stt.android.social.userprofileV2.BaseUserProfileActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class OthersFriendsActivity : AppCompatActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentWithM3Theme {
            OthersFriendsScreen(
                onBackClick = ::finish,
                onFriendClick = ::onFriendClick,
            )
        }
    }

    private fun onFriendClick(friend: Friend) {
        startActivity(
            BaseUserProfileActivity.newStartIntent(
                this,
                friend.username,
                false,
            )
        )
    }

    companion object {
        internal const val KEY_USERNAME: String = "username"
        internal const val KEY_SHOW_FOLLOWER: String = "showFollower"

        fun newStartIntent(context: Context, username: String, showFollower: Boolean = false): Intent {
            return Intent(context, OthersFriendsActivity::class.java)
                .putExtra(KEY_USERNAME, username)
                .putExtra(KEY_SHOW_FOLLOWER, showFollower)
        }
    }
}

package com.stt.android.social.badges.badgesDetail

import android.annotation.SuppressLint
import com.stt.android.data.badges.BadgeAcquisitionCondition
import com.stt.android.data.badges.BadgeConditionName
import com.stt.android.data.badges.BadgeConditionType
import com.stt.android.data.badges.ExploreMore
import com.stt.android.data.badges.UserBadge
import com.stt.android.infomodel.SummaryItem
import com.stt.android.mapping.InfoModelFormatter
import com.stt.android.social.badges.badgesUseCase.BadgesDetailUseCase
import javax.inject.Inject

class BadgeDetailDataLoader @Inject constructor(
    private val badgesDetailUseCase: BadgesDetailUseCase,
    private val infoModelFormatter: InfoModelFormatter,
) {
    suspend fun loadBadgesDetailData(badgeConfigId: String?): UserBadge {
        val userBadge: UserBadge = badgesDetailUseCase.getUserBadgeDetail(badgeConfigId)
        return userBadge
    }

    suspend fun loadExploreMoreData(badgeConfigId: String?): List<ExploreMore> {
        val exploreMore: List<ExploreMore> =
            badgesDetailUseCase.getMoreBadgesInDetail(badgeConfigId)
        return exploreMore.take(3)
    }

    suspend fun loadConditionsData(badgeConfigId: String?): List<BadgeProgressValueWithTarget> {
        val userBadge: UserBadge = badgesDetailUseCase.getUserBadgeDetail(badgeConfigId)
        val leafConditions = extractLeafConditions(userBadge.acquisitionCondition)
        return leafConditions.mapNotNull { condition ->
            val current = getCurrentValue(userBadge, condition.conditionName)
            val target = condition.targetVal ?: return@mapNotNull null
            if (current.isNotEmpty() && target.isNotEmpty()) {
                BadgeProgressValueWithTarget(current, target, condition.conditionName)
            } else null
        }
    }

    private fun extractLeafConditions(condition: BadgeAcquisitionCondition?): List<BadgeAcquisitionCondition> {
        if (condition == null) return emptyList()
        if (condition.type == BadgeConditionType.LEAF) return listOf(condition)
        return condition.childConditions?.flatMap { extractLeafConditions(it) } ?: emptyList()
    }

    @SuppressLint("DefaultLocale")
    private fun getCurrentValue(userBadge: UserBadge, conditionName: BadgeConditionName?): String {
        return when (conditionName) {
            BadgeConditionName.DURATION -> userBadge.totalDuration?.let { duration ->
                val seconds = duration / 1000.0
                infoModelFormatter.formatValueAsString(SummaryItem.DURATION, seconds)
            } ?: ""

            BadgeConditionName.DISTANCE -> userBadge.totalDistance?.let { distance ->
                val meters = distance / 100.0
                infoModelFormatter.formatValueAsString(SummaryItem.DISTANCE, meters)
            } ?: ""

            BadgeConditionName.ASCENT -> userBadge.totalAscent?.let { ascent ->
                val meters = ascent / 100.0
                infoModelFormatter.formatValueAsString(SummaryItem.ASCENTALTITUDE, meters)
            } ?: ""

            BadgeConditionName.ENERGY -> userBadge.energy?.let { energy ->
                infoModelFormatter.formatValueAsString(SummaryItem.ENERGY, energy)
            } ?: ""

            BadgeConditionName.WORKOUT_NUMBER -> userBadge.activitySessions?.toString() ?: ""

            BadgeConditionName.TOTAL_UTMB_DISTANCE -> userBadge.totalUTMBDistance?.let { utmbDistance ->
                val meters = utmbDistance / 100.0
                infoModelFormatter.formatValueAsString(SummaryItem.DISTANCE, meters)
            } ?: ""

            else -> ""
        }
    }
}


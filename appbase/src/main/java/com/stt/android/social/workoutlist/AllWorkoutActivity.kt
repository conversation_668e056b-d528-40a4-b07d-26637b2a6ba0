package com.stt.android.social.workoutlist

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import androidx.activity.SystemBarStyle
import androidx.activity.enableEdgeToEdge
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.stt.android.R
import com.stt.android.analytics.AnalyticsEvent
import com.stt.android.analytics.AnalyticsEventProperty
import com.stt.android.analytics.AnalyticsPropertyValue
import com.stt.android.compose.component.SuuntoIconButton
import com.stt.android.compose.theme.SuuntoIcons
import com.stt.android.compose.util.setContentWithM3Theme
import com.stt.android.databinding.ComposeMapSnapshotterBinding
import com.stt.android.di.navigation.WorkoutDetailsRewriteNavigator
import com.stt.android.eventtracking.EventTracker
import com.stt.android.home.dashboardv2.usecase.GenerateShareWorkoutUseCase
import com.stt.android.home.dashboardv2.usecase.ReactToWorkoutUseCase
import com.stt.android.maps.MapSnapshotter
import com.stt.android.social.userprofileV2.BaseUserProfileActivity.Companion.KEY_USER_NAME
import com.stt.android.social.workoutlist.search.SearchWorkoutActivity
import com.stt.android.social.workoutlist.ui.PostViewerScreen
import com.stt.android.social.workoutlist.ui.WorkoutListTab
import com.stt.android.ui.components.workout.actions.rememberWorkoutCardActionsHandler
import com.stt.android.tags.TagsNavigator
import com.stt.android.workouts.sharepreview.customshare.WorkoutShareHelper
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class AllWorkoutActivity : AppCompatActivity() {
    @Inject
    lateinit var rewriteNavigator: WorkoutDetailsRewriteNavigator

    @Inject
    lateinit var mapSnapshotter: MapSnapshotter

    @Inject
    lateinit var eventTracker: EventTracker

    private val binding: ComposeMapSnapshotterBinding by lazy {
        ComposeMapSnapshotterBinding.inflate(layoutInflater)
    }

    private val allWorkoutViewModel: AllWorkoutViewModel by viewModels()

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                mapSnapshotter.runSnapshotterEngine(this@AllWorkoutActivity)
            }
        }
        setContentView(binding.root)
        binding.composeView.setContentWithM3Theme {
            var selectedPostIndex by remember { mutableStateOf<Int?>(null) }

            val viewState by allWorkoutViewModel.viewState.collectAsState()
            val photos = (viewState as? AllWorkoutViewModel.ViewData.Loaded)?.posts
                ?: emptyList()
            LaunchedEffect(photos) {
                if (photos.none()) {
                    selectedPostIndex = null
                }
            }

            Box(modifier = Modifier.fillMaxSize()) {
                WorkoutListScreen(
                    onBackClicked = ::finish,
                    onWorkoutPostClick = { index, photo ->
                        selectedPostIndex = index
                    },
                )
                // Full screen post viewer
                selectedPostIndex?.let { index ->
                    enableEdgeToEdge(
                        statusBarStyle = SystemBarStyle.dark(Color.TRANSPARENT),
                        navigationBarStyle = SystemBarStyle.dark(Color.TRANSPARENT),
                    )
                    PostViewerScreen(
                        workoutPosts = photos,
                        initialPostIndex = index,
                        onBackClick = {
                            selectedPostIndex = null
                            enableEdgeToEdge(
                                statusBarStyle = SystemBarStyle.light(
                                    Color.TRANSPARENT,
                                    Color.BLACK,
                                ),
                                navigationBarStyle = SystemBarStyle.light(
                                    Color.TRANSPARENT,
                                    Color.BLACK,
                                ),
                            )
                        },
                        onCheckActivities = {
                            rewriteNavigator.navigate(
                                context = this@AllWorkoutActivity,
                                username = it.username,
                                workoutId = it.workoutId,
                                workoutKey = it.workoutKey,
                                analyticsSource = AnalyticsPropertyValue.WorkoutDetailsSourceProperty.PERSONAL_POSTS,
                            )
                        }
                    )
                }
            }
        }
    }

    @Composable
    fun WorkoutListScreen(
        onBackClicked: () -> Unit,
        modifier: Modifier = Modifier,
        onWorkoutPostClick: ((index: Int, photo: WorkoutPost) -> Unit)? = null
    ) {
        val viewState by allWorkoutViewModel.viewState.collectAsState()

        val snackbarHostState = remember { SnackbarHostState() }

        val workoutCardActionsHandler = rememberWorkoutCardActionsHandler(
            workoutCardActionsHandler = allWorkoutViewModel.workoutCardActionsHandler,
            fragmentManager = supportFragmentManager,
            snackbarHostState = snackbarHostState,
            analyticsSource = AnalyticsPropertyValue.WorkoutDetailsSourceProperty.PERSONAL_PROFILE_WORKOUT_LIST,
        )

        Scaffold(
            topBar = {
                WorkoutListToolbar(
                    onBackClicked = onBackClicked,
                )
            },
            snackbarHost = {
                SnackbarHost(snackbarHostState)
            },
            modifier = modifier,
        ) { paddingValues ->
            WorkoutListTab(
                viewState = viewState,
                workoutCardActionsHandler = workoutCardActionsHandler,
                modifier = Modifier.padding(paddingValues),
                onWorkoutPostClick = onWorkoutPostClick,
                onRetryClicked = allWorkoutViewModel::load,
                measurementUnit = allWorkoutViewModel.measurementUnit,
                lastOperationHistory = allWorkoutViewModel.lastOperationHistoryState.value,
                onTabSelected = allWorkoutViewModel::onTabSelected,
                onFilterSelected = allWorkoutViewModel::onFilterSelected,
            )
        }
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    private fun WorkoutListToolbar(
        onBackClicked: () -> Unit,
        modifier: Modifier = Modifier
    ) {
        TopAppBar(
            title = {
                Text(
                    text = stringResource(id = R.string.all_activities_title).uppercase(),
                )
            },
            modifier = modifier,
            navigationIcon = {
                SuuntoIconButton(
                    icon = SuuntoIcons.ActionBack,
                    onClick = onBackClicked,
                    contentDescription = stringResource(R.string.back),
                )
            },
            actions = {
                IconButton(
                    onClick = {
                        eventTracker.trackEvent(
                            AnalyticsEvent.SEARCH_CLICK,
                            mapOf(AnalyticsEventProperty.PAGE_NAME to allWorkoutViewModel.trackPageName)
                        )
                        startActivity(
                            SearchWorkoutActivity.newStartIntent(
                                this@AllWorkoutActivity,
                                allWorkoutViewModel.username,
                                allWorkoutViewModel.trackPageName,
                            )
                        )
                    },
                ) {
                    Icon(
                        painter = painterResource(com.stt.android.compose.ui.R.drawable.search_outline),
                        contentDescription = stringResource(R.string.search),
                    )
                }
            }
        )
    }

    companion object {
        @JvmStatic
        fun newStartIntent(context: Context, username: String, trackPageName: String): Intent {
            return Intent(context, AllWorkoutActivity::class.java)
                .putExtra(KEY_USER_NAME, username)
                .putExtra(AnalyticsEventProperty.PAGE_NAME, trackPageName)
        }
    }
}

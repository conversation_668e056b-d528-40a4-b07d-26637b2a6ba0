package com.stt.android.easterEgg

import android.app.Activity
import android.content.Context
import android.os.Environment
import android.widget.Toast
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.stt.android.STTApplication
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.data.source.local.DaoFactory
import com.stt.android.domain.database.DatabaseHelper
import com.stt.android.logging.TimberInMemoryTree
import com.stt.android.refreshable.Refreshables
import com.stt.android.ui.activities.SimpleAlertDialog
import com.stt.android.utils.FileUtils
import com.stt.android.utils.STTConstants
import com.stt.android.workouts.autosave.AutoSaveOngoingWorkoutController
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.io.IOException

/**
 * Easter Egg base implementation.
 */
abstract class EasterEggBase(
    private val timberInMemoryTree: TimberInMemoryTree,
    private val refreshables: Refreshables,
    private val coroutinesDispatchers: CoroutinesDispatchers,
) {
    private var loggingActivated: Boolean = false

    suspend fun easterEggToggle(application: STTApplication, activity: Activity): Unit = withContext(coroutinesDispatchers.io) {
        // Ensure logging files can be created.
        val dst = getSttDumpFolder(application)
        if (dst == null) {
            withContext(coroutinesDispatchers.main) {
                showOkAlertDialog(
                    activity,
                    """Unable to create logging files. Ensure, application
                    | has permission to write files in android settings
                """.trimMargin()
                )
            }
            return@withContext
        }
        // Toggle logging state.
        loggingActivated = !loggingActivated

        // Request application to start or stop logging.
        application.logToFile(application, loggingActivated)
        withContext(coroutinesDispatchers.main) {
            Toast.makeText(activity,
                if (loggingActivated) "Logging started" else "Logging stopped",
                Toast.LENGTH_SHORT
            ).show()
        }

        runSuspendCatching {
            setFlavorSpecificFileLogging(application, loggingActivated)
            copyInternalData(application, activity)
            createErrorReport()
            withContext(coroutinesDispatchers.main) {
                refreshables.refresh(skipRateLimiting = true)
            }
        }.onFailure { e ->
            Timber.w(e, "Failed to dump internal state")

            withContext(coroutinesDispatchers.main) {
                Toast.makeText(activity, "Unable to dump internal state", Toast.LENGTH_LONG).show()
            }
        }
    }

    @Throws(IOException::class)
    private suspend fun copyInternalData(context: Context, activity: Activity) {
        val dumpFolder = copyLoggingDataToDumpFolder(context) ?: return

        val text = "Data dump can be found at '$dumpFolder'"
        Timber.i(text)
        withContext(coroutinesDispatchers.main) {
            showOkAlertDialog(activity, text)
        }
    }

    @Throws(IOException::class)
    private fun copyLoggingDataToDumpFolder(context: Context): String? {
        val dst = getSttDumpFolder(context) ?: return null

        AutoSaveOngoingWorkoutController.dumpFiles(context, dst)

        Timber.i("Dumping database: %s", DatabaseHelper.DATABASE_NAME)
        val src = context.getDatabasePath(DatabaseHelper.DATABASE_NAME)
        FileUtils.copyFile(src, File(dst, src.name), true)
        Timber.i("Database saved")

        Timber.i("Dumping Room database: %s", DaoFactory.DATABASE_FILE_NAME)
        val roomSrc = context.getDatabasePath(DaoFactory.DATABASE_FILE_NAME)
        FileUtils.copyFile(roomSrc, File(dst, roomSrc.name), true)

        try {
            val roomWalSrc = context.getDatabasePath(DaoFactory.DATABASE_FILE_NAME + "-wal")
            FileUtils.copyFile(roomWalSrc, File(dst, roomWalSrc.name), true)
            Timber.i("Dumped Room database WAL file: %s", roomWalSrc.name)
        } catch (e: IOException) {
            Timber.i(e, "Unable to dump Room database WAL file")
        }

        Timber.i("Room database saved")

        val sharedPrefsFolder = File(context.filesDir.toString() + "/../shared_prefs")
        Timber.i("Dumping prefs: %s", sharedPrefsFolder.toString())
        val allSharedPrefs = sharedPrefsFolder.listFiles()
        if (allSharedPrefs != null) {
            for (sharedPrefs in allSharedPrefs) {
                if (sharedPrefs.isFile) {
                    try {
                        FileUtils.copyFile(sharedPrefs, File(dst, sharedPrefs.name), true)
                        Timber.i("Shared preferences %s saved", sharedPrefs.name)
                    } catch (e: IOException) {
                        Timber.w(e, "Unable to dump shared preferences %s", sharedPrefs.name)
                    }
                }
            }
        }

        val workoutsFolder = File(context.filesDir, STTConstants.DIRECTORY_WORKOUTS)
        val allWorkouts = workoutsFolder.listFiles()
        if (allWorkouts != null) {
            for (workoutFile in allWorkouts) {
                if (workoutFile.isFile) {
                    FileUtils.copyFile(workoutFile, File(dst, workoutFile.name), true)
                    Timber.i("Workout %s saved", workoutFile.name)
                }
            }
        }

        // Save also logs from the TimberInMemoryTree instance used by the main process. This will
        // have duplicate lines with "app.log", but include it just in case as it may have useful
        // logs from before the user starting logging via the easter egg.
        timberInMemoryTree.writeLogsToFile(context.filesDir.absolutePath, "ui-process")?.let {
            try {
                FileUtils.copyFile(it, File(dst, "ui-process.log"), true)
                Timber.i("ui-process.log saved")
            } catch (e: IOException) {
                Timber.w(e, "Unable to save UI process in-memory logs")
            }
        }

        return dst.toString()
    }

    private fun createErrorReport() {
        if (!STTConstants.DEBUG) {
            val fakeException = Throwable("Report created during internal dump")
            FirebaseCrashlytics.getInstance().recordException(fakeException)
            Timber.i("Sent silent report")
        }
    }

    private fun showOkAlertDialog(context: Context, message: String) {
        SimpleAlertDialog.showOkDialog(context, ALERT_DIALOG_TITLE, message)
    }

    protected abstract suspend fun setFlavorSpecificFileLogging(context: Context, logToFile: Boolean)

    companion object {
        private const val ALERT_DIALOG_TITLE = "Notice"

        /**
         * Folder in the sdcard where the files will be saved
         */
        const val DUMP_FOLDER_NAME = "stt-dump"

        fun getSttDumpFolder(context: Context): File? {
            if (Environment.getExternalStorageState() != Environment.MEDIA_MOUNTED) {
                Timber.w("Unable to dump internal state. External storage is not ready")
                return null
            }
            val dst = File(context.getExternalFilesDir(null), DUMP_FOLDER_NAME)
            if (!dst.isDirectory) {
                if (!dst.mkdirs()) {
                    Timber.w("Unable to create destination folder %s", dst.toString())
                    return null
                }
            }
            return dst
        }
    }
}

package com.stt.android.intentresolver

import com.stt.android.domain.workouts.WorkoutHeader

sealed class IntentKey {

    class SaveRoute(val workoutHeader: WorkoutHeader) : IntentKey()

    class WorkoutDetails(
        val username: String,
        val workoutId: Int?,
        val workoutKey: String?,
        val analyticsSource: String?,
        val isFromNotification: Boolean,
        val showCommentsDialog: Boolean = false,
        val showMapGraphAnalysis: Boolean = false,
        val autoPlayback: <PERSON>olean = false,
        val forceSkiMap: Boolean = false,
        val hideBarInfo: Boolean = false,
    ) : IntentKey()

    class Library(
        val initialTab: LibraryTab,
        val scrollToRegionId: String? = null
    ) : IntentKey()

    data object DebugWatchLocation : IntentKey()
}

enum class LibraryTab {
    OFFLINE_MAPS,
    ROUTES,
    POIS
}

enum class TopRouteAction {
    NONE,
    ADD_FAVORITE,
    EDIT,
}

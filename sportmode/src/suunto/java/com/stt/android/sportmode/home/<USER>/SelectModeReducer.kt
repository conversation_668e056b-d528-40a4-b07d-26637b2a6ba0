package com.stt.android.sportmode.home.reducer

import android.content.Context
import com.stt.android.sportmode.home.SportHeader
import com.stt.android.sportmode.home.SportModeHomeReducer
import com.stt.android.sportmode.home.SportModeHomeState
import com.stt.android.sportmode.selectmode.SelectModeActivity
import com.stt.android.utils.activityresult.ResultLauncherActivity
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine

class SelectModeReducer(
    private val context: Context,
    private val sportHeader: SportHeader,
) : SportModeHomeReducer {
    override suspend fun invoke(state: SportModeHomeState): SportModeHomeState {
        if (context !is ResultLauncherActivity) return state
        val contract = SelectModeActivity.ResultContract()
        val trainingModeCount = suspendCoroutine { continuation ->
            context.startActivityForResult(
                contract.createIntent(
                    context,
                    sportHeader
                )
            ) { resultCode, data ->
                continuation.resume(contract.parseResult(resultCode, data))
            }
        }
        if (trainingModeCount == 0) return state
        return state.copy(
            sportHeaderList = state.sportHeaderList.map {
                if (it.id == sportHeader.id && it.sportTag == sportHeader.sportTag) {
                    it.copy(
                        modeCount = trainingModeCount
                    )
                } else {
                    it
                }
            }
        )
    }
}
